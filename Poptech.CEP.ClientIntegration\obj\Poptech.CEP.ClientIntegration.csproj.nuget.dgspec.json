{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj", "projectName": "Poptech.CEP.ClientIntegration", "projectPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj", "projectName": "<PERSON>aby", "projectPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Autofac": {"target": "Package", "version": "[8.2.1, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "CuttingEdge.Conditions.NetStandard": {"target": "Package", "version": "[1.2.0, )"}, "Dapper": {"target": "Package", "version": "[2.1.66, )"}, "DistributedLock": {"target": "Package", "version": "[2.6.0, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[3.3.0, )"}, "Flee": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.IdentityModel.Protocols": {"target": "Package", "version": "[8.9.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "System.DirectoryServices.AccountManagement": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}