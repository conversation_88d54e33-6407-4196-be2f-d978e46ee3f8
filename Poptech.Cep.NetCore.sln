﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35707.178
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Webaby", "Webaby\Webaby.csproj", "{407A8453-01D1-499C-B44E-0AFB31B52644}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TinyCRM.Web", "TinyCRM.Web\TinyCRM.Web.csproj", "{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TinyCRM", "TinyCRM\TinyCRM.csproj", "{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TinyCRM.DbMigration", "TinyCRM.DbMigration\TinyCRM.DbMigration.csproj", "{2A626FF2-EC7F-40F1-B2FD-F950B7C7C0B5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TinyCRM.DbMigration.StandardSampleData", "TinyCRM.DbMigration.StandardSampleData\TinyCRM.DbMigration.StandardSampleData.csproj", "{F3B84A63-E273-44FA-9479-7F8F04A2D140}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Migrations", "Migrations", "{FE5A91FD-B825-4897-B9B1-1B70FCE83273}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Poptech.CEP.ClientIntegration", "Poptech.CEP.ClientIntegration\Poptech.CEP.ClientIntegration.csproj", "{DDD154DE-9B4A-4324-9E18-17E9B1DBDC22}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Webaby.Core", "Webaby.Core\Webaby.Core.csproj", "{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TinyCRM.AppServices", "TinyCRM.AppServices\TinyCRM.AppServices.csproj", "{063884BC-0BAC-4898-B22F-DC3F8FDABA5B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{407A8453-01D1-499C-B44E-0AFB31B52644}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{407A8453-01D1-499C-B44E-0AFB31B52644}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{407A8453-01D1-499C-B44E-0AFB31B52644}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{407A8453-01D1-499C-B44E-0AFB31B52644}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A626FF2-EC7F-40F1-B2FD-F950B7C7C0B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A626FF2-EC7F-40F1-B2FD-F950B7C7C0B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A626FF2-EC7F-40F1-B2FD-F950B7C7C0B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A626FF2-EC7F-40F1-B2FD-F950B7C7C0B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3B84A63-E273-44FA-9479-7F8F04A2D140}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3B84A63-E273-44FA-9479-7F8F04A2D140}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3B84A63-E273-44FA-9479-7F8F04A2D140}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3B84A63-E273-44FA-9479-7F8F04A2D140}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDD154DE-9B4A-4324-9E18-17E9B1DBDC22}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDD154DE-9B4A-4324-9E18-17E9B1DBDC22}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDD154DE-9B4A-4324-9E18-17E9B1DBDC22}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDD154DE-9B4A-4324-9E18-17E9B1DBDC22}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{063884BC-0BAC-4898-B22F-DC3F8FDABA5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{063884BC-0BAC-4898-B22F-DC3F8FDABA5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{063884BC-0BAC-4898-B22F-DC3F8FDABA5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{063884BC-0BAC-4898-B22F-DC3F8FDABA5B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2A626FF2-EC7F-40F1-B2FD-F950B7C7C0B5} = {FE5A91FD-B825-4897-B9B1-1B70FCE83273}
		{F3B84A63-E273-44FA-9479-7F8F04A2D140} = {FE5A91FD-B825-4897-B9B1-1B70FCE83273}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FD06911B-2FB8-4171-8F1E-AE6781F1C92B}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = https://poptechvn.visualstudio.com/
		SccLocalPath0 = .
		SccProjectUniqueName1 = Webaby\\Webaby.csproj
		SccProjectName1 = Webaby
		SccLocalPath1 = Webaby
		SccProjectUniqueName2 = TinyCRM.Web\\TinyCRM.Web.csproj
		SccProjectName2 = TinyCRM.Web
		SccLocalPath2 = TinyCRM.Web
		SccProjectUniqueName3 = TinyCRM\\TinyCRM.csproj
		SccProjectName3 = TinyCRM
		SccLocalPath3 = TinyCRM
		SccProjectUniqueName4 = TinyCRM.DbMigration\\TinyCRM.DbMigration.csproj
		SccProjectTopLevelParentUniqueName4 = Poptech.Cep.NetCore.sln
		SccProjectName4 = TinyCRM.DbMigration
		SccLocalPath4 = TinyCRM.DbMigration
		SccProjectUniqueName5 = TinyCRM.DbMigration.StandardSampleData\\TinyCRM.DbMigration.StandardSampleData.csproj
		SccProjectTopLevelParentUniqueName5 = Poptech.Cep.NetCore.sln
		SccProjectName5 = TinyCRM.DbMigration.StandardSampleData
		SccLocalPath5 = TinyCRM.DbMigration.StandardSampleData
		SccProjectUniqueName6 = Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj
		SccProjectName6 = Poptech.CEP.ClientIntegration
		SccLocalPath6 = Poptech.CEP.ClientIntegration
		SccProjectUniqueName7 = Webaby.Core\\Webaby.Core.csproj
		SccProjectName7 = Webaby.Core
		SccLocalPath7 = Webaby.Core
		SccProjectUniqueName8 = TinyCRM.AppServices\\TinyCRM.AppServices.csproj
		SccProjectName8 = TinyCRM.AppServices
		SccLocalPath8 = TinyCRM.AppServices
	EndGlobalSection
EndGlobal
