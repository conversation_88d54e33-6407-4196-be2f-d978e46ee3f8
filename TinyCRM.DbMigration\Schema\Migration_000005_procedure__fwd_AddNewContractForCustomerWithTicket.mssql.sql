
/****** Object:  StoredProcedure [fwd].[AddNewContractForCustomerWithTicket]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROC [fwd].[AddNewContractForCustomerWithTicket]
AS
BEGIN

    -- mỗi lần thêm một hợp đồng
    -- cho mỗi khách hàng đang có phiếu

    INSERT fwd.ContractSyncRawItem (Id, AGENTCODE, --FWD_00_AgencyCode,
                                    POLICY,        --FWD_00_ContractNumber,
                                    PLANCODE,      --FWD_00_ProductCode,
                                    BILL_FREQ,     --FWD_00_FrequencyInputMoney,
                                    STATUS,        --FWD_00_ContractStatus,
                                    SUBMIT_DATE,   --FWD_00_DatePaid,
                                    ISSUE_DATE,    --FWD_00_DateIssued,
                                    Main_FYP,      --FWD_00_Main_FYP,
                                    Main_IP,       --FWD_00_Main_IP,
                                    Main_APE,      --FWD_00_Main_APE,
                                    Rider_FYP,     --FWD_00_Sub_FYP,
                                    Rider_IP,      --FWD_00_Sup_IP,
                                    Rider_APE      --FWD_00_Sup_APE
    )
    SELECT NEWID(), c.Code, 'HD' + CAST(RAND(CHECKSUM(NEWID())) AS NVARCHAR(10)), N'P00' + CAST((ROW_NUMBER() OVER (ORDER BY c.Code)) AS NVARCHAR(10)), 1, 'Issued', [dbo].[Pop_GetDate](),
           DATEFROMPARTS(2022, 3, (2 * ROW_NUMBER() OVER (ORDER BY c.Code)) % 30 + 1), (ABS(CHECKSUM(NEWID()) % 100) + 1) * 1000, 35000000, 120000000, 10000000, 8000000, 40000000
    FROM (SELECT c.Code FROM dbo.Customer c JOIN dbo.RequestTicket rt ON rt.CustomerId = c.Id GROUP BY c.Code) c;

END;
GO