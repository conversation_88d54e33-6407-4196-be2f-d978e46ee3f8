
/****** Object:  StoredProcedure [fwd].[CommitContestExpense]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [fwd].[CommitContestExpense]
    @DynamicFormId UNIQUEIDENTIFIER, @UserId UNIQUEIDENTIFIER
AS
BEGIN
    CREATE TABLE #tempExpenseItems
    (
        Id                    UNIQUEIDENTIFIER,
        CauseOfExpenseId      UNIQUEIDENTIFIER,
        GroupCauseOfExpenseId UNIQUEIDENTIFIER,
        amount                BIGINT,
        PayeeId               UNIQUEIDENTIFIER,
        PayeeType             INT,
        PaymentType           UNIQUEIDENTIFIER,
        E_Status              INT
    );
    INSERT INTO #tempExpenseItems (Id, CauseOfExpenseId, GroupCauseOfExpenseId, amount, PayeeId, PayeeType, PaymentType, E_Status)
    SELECT rt.Id, dfv.Id CauseOfExpenseId, st.Id GroupCauseOfExpenseId, dfv.Value amount, rt.CustomerId PayeeId, 1 PayeeType, dfd.PaymentType, 1 E_Status
    FROM dbo.RequestTicket rt
    JOIN dbo.ServiceType st ON rt.ServiceTypeId = st.Id
    JOIN dbo.DynamicFieldValue dfv ON dfv.DynamicFormValueId = rt.DynamicFormValueId
    JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
    WHERE st.DynamicFormId = @DynamicFormId
          AND dfd.PaymentType IS NOT NULL
          AND rt.Deleted = 0;
    DECLARE @GroupCauseOfExpenseId UNIQUEIDENTIFIER;
    SELECT @GroupCauseOfExpenseId = Id FROM dbo.ServiceType WHERE DynamicFormId = @DynamicFormId;
    --   --SELECT * FROM #tempExpenseItems  WHERE Id = 'D1414F9B-D3EE-432B-A2E0-9667954E7AA1'
    -- xac dinh expenseitem can update
    UPDATE e
    SET E_Status = 2
    FROM #tempExpenseItems e
    JOIN dbo.ExpenseItem ei ON e.CauseOfExpenseId = ei.CauseOfExpenseId
                               AND ei.GroupCauseOfExpenseId = e.GroupCauseOfExpenseId
                               AND ei.PayeeId = e.PayeeId
    WHERE ei.EndorsementId IS NULL;

    -- xac dinh expenseitem đã có endorse
    UPDATE e
    SET E_Status = 3
    FROM #tempExpenseItems e
    JOIN dbo.ExpenseItem ei ON e.CauseOfExpenseId = ei.CauseOfExpenseId
                               AND ei.GroupCauseOfExpenseId = e.GroupCauseOfExpenseId
                               AND ei.PayeeId = e.PayeeId
    WHERE ei.EndorsementId IS NOT NULL;

    -- insert expense mới
    INSERT INTO dbo.ExpenseItem (Id, CauseOfExpenseId, GroupCauseOfExpenseId, Amount, PayeeId, PayeeType, PaymentType, CreatedDate, CreatedBy, Deleted)
    SELECT NEWID(), CauseOfExpenseId, GroupCauseOfExpenseId, amount, PayeeId, PayeeType, PaymentType, [dbo].[Pop_GetDate](), @UserId, 0
    FROM #tempExpenseItems
    WHERE Amount > 0
          AND E_Status = 1;

    -- update expense
    UPDATE ei
    SET ei.Amount = e.amount, ei.ModifiedDate = [dbo].[Pop_GetDate](), ei.PaymentType = e.PaymentType
    FROM #tempExpenseItems e
    JOIN dbo.ExpenseItem ei ON e.CauseOfExpenseId = ei.CauseOfExpenseId
                               AND ei.GroupCauseOfExpenseId = e.GroupCauseOfExpenseId
                               AND ei.PayeeId = e.PayeeId
    WHERE E_Status = 2;

    -- delete item has Amount = 0 and doesnt has ticket
    DELETE ei
    FROM dbo.ExpenseItem ei
    LEFT JOIN #tempExpenseItems allItems ON allItems.GroupCauseOfExpenseId = ei.GroupCauseOfExpenseId
                                            AND ei.CauseOfExpenseId = allItems.CauseOfExpenseId
    WHERE ei.GroupCauseOfExpenseId = @GroupCauseOfExpenseId
          AND ei.EndorsementId IS NULL
          AND allItems.Id IS NULL;

    -- delete item has Amount = 0 
    -- do rule chỉ insert những field có giá trị > 0 nên chỉ khi update mới xuất hiện các giá trị = 0 để xoá ==> DELETE như vậy
    DELETE dbo.ExpenseItem WHERE Amount = 0 AND EndorsementId IS NULL;

    -- summary result
    --SELECT *  FROM #tempExpenseItems --WHERE E_Status = 2
    SELECT SUM(CASE WHEN E_Status = 1 AND amount > 0 THEN 1 ELSE 0 END) NewCount, SUM(CASE WHEN E_Status = 2 THEN 1 ELSE 0 END) UpDateCount, SUM(CASE WHEN E_Status = 3 THEN 1 ELSE 0 END) FailCount
    FROM #tempExpenseItems
    WHERE amount > 0 
END;
GO