
/****** Object:  StoredProcedure [fwd].[DeleteAllPolicies]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[DeleteAllPolicies]

	@ServiceTypeId UNIQUEIDENTIFIER

AS
BEGIN

	DECLARE @DynamicFormId UNIQUEIDENTIFIER
	SELECT	@DynamicFormId = st.DynamicFormId
	FROM	dbo.ServiceType st
	WHERE	st.Id = @ServiceTypeId

	DECLARE @PolicyListDynamicFieldId UNIQUEIDENTIFIER
	SELECT	@PolicyListDynamicFieldId = dfd.Id
	FROM	dbo.DynamicFieldDefinition dfd
	WHERE	dfd.Deleted = 0 AND dfd.DynamicFormId = @DynamicFormId
			AND dfd.DynamicDefinedTableSchemaId IS NOT NULL

	DELETE	cell
	FROM	dbo.DynamicDefinedTableCellValue cell
			JOIN dbo.DynamicFieldValue dfv WITH(NOLOCK) ON dfv.Id = cell.DynamicFieldValueId AND dfv.DynamicFieldId = @PolicyListDynamicFieldId
			JOIN dbo.RequestTicket rt WITH(NOLOCK) ON dfv.DynamicFormValueId = rt.DynamicFormValueId
	WHERE	rt.ServiceTypeId = @ServiceTypeId
			AND rt.Deleted = 0

END
GO