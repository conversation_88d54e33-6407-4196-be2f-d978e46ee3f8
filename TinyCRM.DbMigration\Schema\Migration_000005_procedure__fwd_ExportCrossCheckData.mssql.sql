
/****** Object:  StoredProcedure [fwd].[ExportCrossCheckData]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [fwd].[ExportCrossCheckData]

	@DynamicFormId UNIQUEIDENTIFIER,
	@DynamicFieldList dbo.IdList READONLY,
	@AgencyCode VARCHAR(100)
AS
BEGIN

	DECLARE @ServiceTypeId UNIQUEIDENTIFIER
	SELECT	@ServiceTypeId = st.Id
	FROM	dbo.ServiceType st WITH(NOLOCK)
	WHERE	st.DynamicFormId = @DynamicFormId

	SELECT	DISTINCT dfd.VersionCode
	INTO	#TempVersionCodeList
	FROM	dbo.DynamicFieldDefinition dfd WITH(NOLOCK)
			JOIN @DynamicFieldList dfdParam ON dfdParam.Id = dfd.Id
	WHERE	dfd.DynamicFormId = @DynamicFormId AND ISNULL(dfd.VersionCode,'') <> ''

	DECLARE @FinalAgencyTreeSelect NVARCHAR(MAX) = N''
	DECLARE @AgencyReferenceCodeSelect NVARCHAR(MAX) = N''
	DECLARE @AgencyVersionCte NVARCHAR(MAX) = N''
	DECLARE @AgencyVersionJoins NVARCHAR(MAX) = N''

	IF EXISTS (SELECT * FROM #TempVersionCodeList) BEGIN
		
		DECLARE @AgencyTreeFWO NVARCHAR(MAX) = N''
		DECLARE @AgencyTreeFWM NVARCHAR(MAX) = N''
		DECLARE @AgencyTreeFWD NVARCHAR(MAX) = N''
		DECLARE @AgencyTreeFWDLT NVARCHAR(MAX) = N''
		DECLARE @AgencyTreeAD NVARCHAR(MAX) = N''

		SET @AgencyReferenceCodeSelect = N',
		CASE '

		WHILE EXISTS (SELECT * FROM #TempVersionCodeList) BEGIN

			DECLARE @VersionCode VARCHAR(100)
			SELECT TOP 1 @VersionCode = VersionCode FROM #TempVersionCodeList ORDER BY VersionCode

			SET @AgencyTreeFWO = @AgencyTreeFWO + N'
			WHEN dfd.VersionCode = ''' + @VersionCode + ''' THEN IIF([cte__' + @VersionCode + '].FWO=[cte__' + @VersionCode + '].Code,'''',[cte__' + @VersionCode + '].FWO) '

			SET @AgencyTreeFWM = @AgencyTreeFWM + N'
			WHEN dfd.VersionCode = ''' + @VersionCode + ''' THEN IIF([cte__' + @VersionCode + '].FWM=[cte__' + @VersionCode + '].Code,'''',[cte__' + @VersionCode + '].FWM) '

			SET @AgencyTreeFWD = @AgencyTreeFWD + N'
			WHEN dfd.VersionCode = ''' + @VersionCode + ''' THEN IIF([cte__' + @VersionCode + '].FWD=[cte__' + @VersionCode + '].Code,'''',[cte__' + @VersionCode + '].FWD) '

			SET @AgencyTreeFWDLT = @AgencyTreeFWDLT + N'
			WHEN dfd.VersionCode = ''' + @VersionCode + ''' THEN IIF([cte__' + @VersionCode + '].FWDLT=[cte__' + @VersionCode + '].Code,'''',[cte__' + @VersionCode + '].FWDLT) '

			SET @AgencyTreeAD = @AgencyTreeAD + N'
			WHEN dfd.VersionCode = ''' + @VersionCode + ''' THEN IIF([cte__' + @VersionCode + '].AD=[cte__' + @VersionCode + '].Code,'''',[cte__' + @VersionCode + '].AD) '

			SET @AgencyReferenceCodeSelect = @AgencyReferenceCodeSelect + N'
			WHEN dfd.VersionCode = ''' + @VersionCode + ''' THEN [cte__' + @VersionCode + '].ReferenceCode '

			SET @AgencyVersionCte = @AgencyVersionCte + N',
			[cte__' + @VersionCode + '] AS
			(
				SELECT	cv.CustomerId, cv.Code, cv.Name, cv.Job, cv.Nation,
						IIF(ISNULL(cv.Job,'''') = ''FWO'', cv.Code, CAST('''' AS NVARCHAR(100))) FWO, IIF(ISNULL(cv.Job,'''') = ''FWM'', cv.Code, CAST('''' AS NVARCHAR(100))) FWM, IIF(ISNULL(cv.Job,'''') = ''FWD'', cv.Code, CAST('''' AS NVARCHAR(100))) FWD, IIF(ISNULL(cv.Job,'''') = ''FWDLT'', cv.Code, CAST('''' AS NVARCHAR(100))) FWDLT, IIF(ISNULL(cv.Job,'''') = ''AD'', cv.Code, CAST('''' AS NVARCHAR(100))) AD,
						cv.IncomeSource ReferenceCode
				FROM	dbo.CustomerVersioning cv
						LEFT JOIN dbo.CustomerVersioning pcv ON pcv.Version = CAST(''' + @VersionCode + ''' AS VARCHAR(20)) AND pcv.Code = cv.Nation
				WHERE	cv.Deleted = 0 AND cv.Version = CAST(''' + @VersionCode + ''' AS VARCHAR(20))
						AND pcv.Id IS NULL
				UNION ALL
				SELECT	cv.CustomerId, cv.Code, cv.Name, cv.Job, cv.Nation,
			
						CASE
							WHEN ISNULL(parent.FWO,'''') <> '''' THEN parent.FWO
							WHEN ISNULL(cv.Job,'''') = ''FWO'' THEN cv.Code
							ELSE ''''
						END,
						CASE
							WHEN ISNULL(parent.FWM,'''') <> '''' THEN parent.FWM
							WHEN ISNULL(cv.Job,'''') = ''FWM'' THEN cv.Code
							ELSE ''''
						END,
						CASE
							WHEN ISNULL(parent.FWD,'''') <> '''' THEN parent.FWD
							WHEN ISNULL(cv.Job,'''') = ''FWD'' THEN cv.Code
							ELSE ''''
						END,
						CASE
							WHEN ISNULL(parent.FWDLT,'''') <> '''' THEN parent.FWDLT
							WHEN ISNULL(cv.Job,'''') = ''FWDLT'' THEN cv.Code
							ELSE ''''
						END,
						parent.AD,
						cv.IncomeSource ReferenceCode

				FROM	dbo.CustomerVersioning cv
						join [cte__' + @VersionCode + '] parent on parent.Code = cv.Nation
				WHERE	cv.Deleted = 0 AND cv.Version = CAST(''' + @VersionCode + ''' AS VARCHAR(20))
			) '

			SET @AgencyVersionJoins = @AgencyVersionJoins + N'
			LEFT JOIN [cte__' + @VersionCode + '] ON [cte__' + @VersionCode + '].CustomerId = c.Id '

			DELETE #TempVersionCodeList WHERE VersionCode = @VersionCode

		END

		SET @FinalAgencyTreeSelect = N',
		CASE
			' + @AgencyTreeFWO + '
			ELSE ''''
		END FWO,
		CASE
			' + @AgencyTreeFWM + '
			ELSE ''''
		END FWM,
		CASE
			' + @AgencyTreeFWD + '
			ELSE ''''
		END FWD,
		CASE
			' + @AgencyTreeFWDLT + '
			ELSE ''''
		END FWDLT,
		CASE
			' + @AgencyTreeAD + '
			ELSE ''''
		END AD '

		SET @AgencyReferenceCodeSelect = @AgencyReferenceCodeSelect + N'
		ELSE ''''
		END [Mã Agency Giới thiệu] '

	END
	ELSE BEGIN
		SET @FinalAgencyTreeSelect = N', c.FWO, c.FWM, c.FWD, c.FWDLT, c.AD'
		SET @AgencyReferenceCodeSelect = N', cus.IncomeSource ReferenceCode '
	END

	DECLARE @ExecutedString NVARCHAR(MAX) = N'
	WITH [cte__Customer] AS
	(
		SELECT	cv.Id, cv.Code, cv.Name, cv.Job, cv.Nation, 1 as [NodeLevel], CAST(1 AS Float) as TempDivide, cast(ROW_NUMBER() over (order by cv.Code) as float) ListAllDisplayOrder,
				IIF(ISNULL(cv.Job,'''') = ''FWO'', cv.Code, CAST('''' AS NVARCHAR(100))) FWO, IIF(ISNULL(cv.Job,'''') = ''FWM'', cv.Code, CAST('''' AS NVARCHAR(100))) FWM, IIF(ISNULL(cv.Job,'''') = ''FWD'', cv.Code, CAST('''' AS NVARCHAR(100))) FWD, IIF(ISNULL(cv.Job,'''') = ''FWDLT'', cv.Code, CAST('''' AS NVARCHAR(100))) FWDLT, IIF(ISNULL(cv.Job,'''') = ''AD'', cv.Code, CAST('''' AS NVARCHAR(100))) AD
		FROM	dbo.Customer cv
				LEFT JOIN dbo.Customer pcv ON pcv.Code = cv.Nation
		WHERE	cv.Deleted = 0
				AND pcv.Id IS NULL
		UNION ALL
		SELECT	cv.Id, cv.Code, cv.Name, cv.Job, cv.Nation, parent.[NodeLevel] + 1, 1000 * parent.TempDivide, parent.ListAllDisplayOrder + cast(ROW_NUMBER() over (order by cv.Code) as float)/(1000 * parent.TempDivide),
			
				CASE
					WHEN ISNULL(parent.FWO,'''') <> '''' THEN parent.FWO
					WHEN ISNULL(cv.Job,'''') = ''FWO'' THEN cv.Code
					ELSE ''''
				END,
				CASE
					WHEN ISNULL(parent.FWM,'''') <> '''' THEN parent.FWM
					WHEN ISNULL(cv.Job,'''') = ''FWM'' THEN cv.Code
					ELSE ''''
				END,
				CASE
					WHEN ISNULL(parent.FWD,'''') <> '''' THEN parent.FWD
					WHEN ISNULL(cv.Job,'''') = ''FWD'' THEN cv.Code
					ELSE ''''
				END,
				CASE
					WHEN ISNULL(parent.FWDLT,'''') <> '''' THEN parent.FWDLT
					WHEN ISNULL(cv.Job,'''') = ''FWDLT'' THEN cv.Code
					ELSE ''''
				END,
				parent.AD

		FROM	dbo.Customer cv
				join [cte__Customer] parent on parent.Code = cv.Nation
		WHERE	cv.Deleted = 0
	) ' + @AgencyVersionCte + N'
	SELECT	c.NodeLevel, c.Name [Tên Agency], c.Code [Mã Agency], c.Job [Cấp bậc], rt.Code [Mã phiếu tham dự], dfd.DisplayName [Trường thông tin], dfd.VersionCode [Cấu trúc tháng], dfv.Value [Giá trị] ' + @AgencyReferenceCodeSelect + @FinalAgencyTreeSelect + N'
	FROM	dbo.RequestTicket rt WITH(NOLOCK)
			JOIN [cte__Customer] c WITH(NOLOCK) ON c.Id = rt.CustomerId
			JOIN dbo.Customer cus WITH(NOLOCK) ON cus.Id = c.Id
			JOIN dbo.DynamicFormValue dfrv WITH(NOLOCK) ON dfrv.Id = rt.DynamicFormValueId
			JOIN dbo.DynamicForm df WITH(NOLOCK) ON df.Id = dfrv.DynamicFormId
			JOIN dbo.DynamicFieldDefinition dfd WITH(NOLOCK) ON dfd.DynamicFormId = df.Id
			JOIN @DynamicFieldList dfdParams ON dfdParams.Id = dfd.Id
			JOIN dbo.DynamicFieldValue dfv WITH(NOLOCK) ON dfv.DynamicFieldId = dfd.Id AND dfv.DynamicFormValueId = dfrv.Id ' + @AgencyVersionJoins + N'
	WHERE	rt.Deleted = 0
			AND rt.ServiceTypeId = @ServiceTypeId '

	IF ISNULL(@AgencyCode,'') <> '' BEGIN
		SET @ExecutedString = @ExecutedString + N'
		AND (c.Code = @AgencyCode OR c.FWO = @AgencyCode OR c.FWM = @AgencyCode OR c.FWD = @AgencyCode OR c.FWDLT = @AgencyCode OR c.AD = @AgencyCode) '
	END

	SET @ExecutedString = @ExecutedString + N'
	ORDER BY c.ListAllDisplayOrder, dfd.[Order] '

	DECLARE @ParmDefinition NVARCHAR(MAX) = N'
	@ServiceTypeId UNIQUEIDENTIFIER,
	@DynamicFormId UNIQUEIDENTIFIER,
	@DynamicFieldList dbo.IdList READONLY,
	@AgencyCode VARCHAR(100)'

	EXEC dbo.Proc_PrintLongText @LongString = @ExecutedString	

	EXECUTE sp_executesql @ExecutedString, @ParmDefinition,  
											@ServiceTypeId = @ServiceTypeId,
											@DynamicFormId = @DynamicFormId,
											@DynamicFieldList = @DynamicFieldList,
											@AgencyCode = @AgencyCode

END
GO