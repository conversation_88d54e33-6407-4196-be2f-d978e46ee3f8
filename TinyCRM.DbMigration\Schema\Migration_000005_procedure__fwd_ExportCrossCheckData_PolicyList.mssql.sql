
/****** Object:  StoredProcedure [fwd].[ExportCrossCheckData_PolicyList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[ExportCrossCheckData_PolicyList]

	@DynamicDefinedTableSchemaId UNIQUEIDENTIFIER,
	@AgencyCode VARCHAR(100)

AS BEGIN

	DECLARE @PolicyColumnId UNIQUEIDENTIFIER
	SELECT	@PolicyColumnId = col.Id
	FROM	dbo.DynamicDefinedTableColumn col WITH(NOLOCK)
	WHERE	col.DynamicDefinedTableSchemaId = @DynamicDefinedTableSchemaId
			AND col.Name = 'POLICY'

	SELECT	col.Id, col.Name, col.DisplayName, col.DataType, col.ColumnOrder
	INTO	#TempSelectedColumn
	FROM	dbo.DynamicDefinedTableColumn col
			JOIN sys.columns tblCol ON tblCol.Name = col.Name AND tblCol.OBJECT_ID = OBJECT_ID('fwd.ContractSyncRawItem')
	WHERE	col.DynamicDefinedTableSchemaId = @DynamicDefinedTableSchemaId
			AND col.Deleted = 0
			 AND col.Name <> 'AGENTCODE'

	DECLARE @ColumnSelectString NVARCHAR(MAX) = N''
	WHILE EXISTS (SELECT * FROM #TempSelectedColumn) BEGIN

		DECLARE @ColumnId UNIQUEIDENTIFIER
		DECLARE @ColumnName VARCHAR(500)
		DECLARE @ColumnDisplayName NVARCHAR(1000)
		DECLARE @ColumnDataType NVARCHAR(1000)

		SELECT	TOP 1 @ColumnId = Id, @ColumnName = Name, @ColumnDisplayName = DisplayName, @ColumnDataType = DataType
		FROM	#TempSelectedColumn
		ORDER BY ColumnOrder

		IF CHARINDEX('DateTime', @ColumnDataType) > 0 BEGIN
			SET @ColumnSelectString = @ColumnSelectString + ', FORMAT(rawPl.' + @ColumnName + ', ''dd/MM/yyyy'') [' + @ColumnDisplayName + ']'
		END
		ELSE BEGIN
			SET @ColumnSelectString = @ColumnSelectString + ', rawPl.' + @ColumnName + ' [' + @ColumnDisplayName + ']'
		END

		DELETE #TempSelectedColumn WHERE Id = @ColumnId

	END

	DROP TABLE #TempSelectedColumn

	DECLARE @ExecutedString NVARCHAR(MAX) = N'
	WITH [cte__Customer] AS
	(
		SELECT	cv.Id, cv.Code, cv.Name, cv.Job, cv.Nation, 1 as [NodeLevel], CAST(1 AS BIGINT) as TempDivide, cast(ROW_NUMBER() over (order by cv.Code) as float) ListAllDisplayOrder,
				IIF(ISNULL(cv.Job,'''') = ''FWO'', cv.Code, CAST('''' AS NVARCHAR(100))) FWO, IIF(ISNULL(cv.Job,'''') = ''FWM'', cv.Code, CAST('''' AS NVARCHAR(100))) FWM, IIF(ISNULL(cv.Job,'''') = ''FWD'', cv.Code, CAST('''' AS NVARCHAR(100))) FWD, IIF(ISNULL(cv.Job,'''') = ''FWDLT'', cv.Code, CAST('''' AS NVARCHAR(100))) FWDLT, IIF(ISNULL(cv.Job,'''') = ''AD'', cv.Code, CAST('''' AS NVARCHAR(100))) AD
		FROM	dbo.Customer cv WITH(NOLOCK)
				LEFT JOIN dbo.Customer pcv ON pcv.Code = cv.Nation
		WHERE	cv.Deleted = 0
				AND pcv.Id IS NULL
		UNION ALL
		SELECT	cv.Id, cv.Code, cv.Name, cv.Job, cv.Nation, parent.[NodeLevel] + 1, 1000 * parent.TempDivide, parent.ListAllDisplayOrder + cast(ROW_NUMBER() over (order by cv.Code) as float)/(1000 * parent.TempDivide),
				CASE
					WHEN ISNULL(parent.FWO,'''') <> '''' THEN parent.FWO
					WHEN ISNULL(cv.Job,'''') = ''FWO'' THEN cv.Code
					ELSE ''''
				END,
				CASE
					WHEN ISNULL(parent.FWM,'''') <> '''' THEN parent.FWM
					WHEN ISNULL(cv.Job,'''') = ''FWM'' THEN cv.Code
					ELSE ''''
				END,
				CASE
					WHEN ISNULL(parent.FWD,'''') <> '''' THEN parent.FWD
					WHEN ISNULL(cv.Job,'''') = ''FWD'' THEN cv.Code
					ELSE ''''
				END,
				CASE
					WHEN ISNULL(parent.FWDLT,'''') <> '''' THEN parent.FWDLT
					WHEN ISNULL(cv.Job,'''') = ''FWDLT'' THEN cv.Code
					ELSE ''''
				END,
				parent.AD
		FROM	dbo.Customer cv WITH(NOLOCK)
				join [cte__Customer] parent on parent.Code = cv.Nation
		WHERE	cv.Deleted = 0
	),
	policyList AS
	(
		SELECT	cell.DynamicFieldValueId, cell.RowNumber, CAST(cell.Value AS NVARCHAR(512)) Value
		FROM	dbo.DynamicDefinedTableCellValue cell WITH(NOLOCK)
		WHERE	cell.DynamicDefinedTableColumnId = @PolicyColumnId
	)
	SELECT	c.NodeLevel, c.Name [Tên Agency], c.Code [Mã Agency], c.Job [Cấp bậc]' + @ColumnSelectString + '
	FROM	policyList pl
			JOIN fwd.ContractSyncRawItem rawPl WITH(NOLOCK) ON rawPl.POLICY = pl.Value
			JOIN cte__Customer c ON c.Code = rawPl.AGENTCODE '

	IF ISNULL(@AgencyCode,'') <> '' BEGIN
		SET @ExecutedString = @ExecutedString + N'
		AND (c.Code = @AgencyCode OR c.FWO = @AgencyCode OR c.FWM = @AgencyCode OR c.FWD = @AgencyCode OR c.FWDLT = @AgencyCode OR c.AD = @AgencyCode) '
	END

	SET @ExecutedString = @ExecutedString + N'
	ORDER BY c.ListAllDisplayOrder '

	DECLARE @ParmDefinition NVARCHAR(MAX) = N'
	@AgencyCode VARCHAR(100),
	@DynamicDefinedTableSchemaId UNIQUEIDENTIFIER,
	@PolicyColumnId UNIQUEIDENTIFIER'

	EXEC dbo.Proc_PrintLongText @LongString = @ExecutedString -- nvarchar(max)

	EXECUTE sp_executesql @ExecutedString, @ParmDefinition,
											@AgencyCode = @AgencyCode,
											@DynamicDefinedTableSchemaId = @DynamicDefinedTableSchemaId,
											@PolicyColumnId = @PolicyColumnId

END
GO