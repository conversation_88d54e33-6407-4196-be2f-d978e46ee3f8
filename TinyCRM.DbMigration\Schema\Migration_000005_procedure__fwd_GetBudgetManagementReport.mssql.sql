
/****** Object:  StoredProcedure [fwd].[GetBudgetManagementReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetBudgetManagementReport]
    @FromDate DATETIME, @ToDate DATETIME
AS
BEGIN
    IF @FromDate IS NULL
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, [dbo].[Pop_GetDate]()), DATEPART(MONTH, [dbo].[Pop_GetDate]()), 1, 0, 0, 0, 0);
    ELSE
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, @FromDate), DATEPART(MONTH, @FromDate), DATEPART(DAY, @FromDate), 0, 0, 0, 0);
    IF @ToDate IS NULL
        SET @ToDate = DATEADD(s, -1, DATEADD(mm, DATEDIFF(m, 0, [dbo].[Pop_GetDate]()) + 1, 0));
    ELSE
        SET @ToDate = DATETIMEFROMPARTS(DATEPART(YEAR, @ToDate), DATEPART(MONTH, @ToDate), DATEPART(DAY, @ToDate), 23, 59, 59, 0);
    DECLARE @sql NVARCHAR(MAX);
    SET @sql
        = N'
SELECT i.MonthId,i.YearMemo,i.MonthMemo,CONVERT(INT,ISNULL(p.fValue,0)) APEPlan,CONVERT(BIGINT,ISNULL( pri.TongDuToan,0)) CostingExpense, CONVERT(INT,ISNULL(a.fValue,0)) APEActual,CONVERT(BIGINT,ISNULL(e.ActualExpense ,0)) ActualExpense FROM dbo.v_Memo_Info i
LEFT JOIN dbo.v_BudgetManagementPlan p ON i.YearMemo = p.Plan_Year AND p.Name = CONCAT(''APE_Plan_'' , i.MonthMemo)
LEFT JOIN dbo.v_BudgetManagementPlan a ON i.YearMemo = a.Plan_Year AND a.Name = CONCAT(''APE_Actual_'' , i.MonthMemo)
LEFT JOIN dbo.v_BudgetManagementReport_ProposalInfo pri ON pri.DynamicFormId = i.DynamicFormId
LEFT JOIN dbo.v_MemoActualExpense e ON e.servicetypeid = i.servicetypeId';
    SET @sql = @sql + N' WHERE i.Thoigianbatdau BETWEEN @FromDate AND @ToDate';
    DECLARE @Params NVARCHAR(MAX) = N'
									@FromDate DATETIME,
									@ToDate DATETIME ';

    --PRINT @sql;
    EXEC sp_executesql @sql, @Params, @FromDate = @FromDate, @ToDate = @ToDate;

END;
GO