
/****** Object:  StoredProcedure [fwd].[GetContestListAPI]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [fwd].[GetContestListAPI]
AS
BEGIN
	SELECT df.Code ContestCode, c.CampaignName ContestName, 'AGENCY' Channelcd, NULL Partnerid, FORMAT(c.StartDate, 'dd/MM/yyyy') StartDate,
	FORMAT(c.EndDate, 'dd/MM/yyyy') EndDate, FORMAT(c.FinalResultDate, 'dd/MM/yyyy') ContestFinalResultDate, c.CampaignParticipants DesignationCode,f.Data FileMemo
	FROM dbo.Campaign c
	JOIN dbo.DynamicForm df ON c.DynamicFormId = df.Id
	LEFT JOIN dbo.[FILE] f ON f.ReferenceObjectId = c.Id
	WHERE c.AutoSendSalePortal = 1
END
GO