
/****** Object:  StoredProcedure [fwd].[GetContestOverviewReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetContestOverviewReport]
    @FromDate DATETIME, @ToDate DATETIME, @MemoCode NVARCHAR(512)
AS
BEGIN
    IF @FromDate IS NULL
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, [dbo].[Pop_GetDate]()), DATEPART(MONTH, [dbo].[Pop_GetDate]()), 1, 0, 0, 0, 0);
    ELSE
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, @FromDate), DATEPART(MONTH, @FromDate), DATEPART(DAY, @FromDate), 0, 0, 0, 0);
    IF @ToDate IS NULL
        SET @ToDate = DATEADD(s, -1, DATEADD(mm, DATEDIFF(m, 0, [dbo].[Pop_GetDate]()) + 1, 0));
    ELSE
        SET @ToDate = DATETIMEFROMPARTS(DATEPART(YEAR, @ToDate), DATEPART(MONTH, @ToDate), DATEPART(DAY, @ToDate), 23, 59, 59, 0);
    DECLARE @sql NVARCHAR(MAX);
    SET @sql
        = N';WITH info
					AS (SELECT * FROM v_Memo_Info WHERE (Thoigianbatdau BETWEEN @FromDate AND @ToDate) ' + IIF(@MemoCode IS NOT NULL AND @MemoCode <> '', ' AND FormCode = @MemoCode ', '')
          + N')
SELECT m.MonthId, m.YearMemo, m.MonthMemo, m.FormCode, m.FormName,ae.ResultType, ae.ActualExpense, SUM(ISNULL(e.EndorsedAmount, 0)) EndorsedAmount, CASE WHEN e.curStatus = 3 THEN ''Pending''
                                                                                                                                           WHEN e.curStatus = 4 THEN ''End'' ELSE '''' END EndorsedStatus,
       SUM(ISNULL(p.PaymentAmount, 0)) PaymentAmount
FROM info m
JOIN v_MemoActualExpense ae ON m.servicetypeId = ae.servicetypeid
LEFT JOIN dbo.v_FullyEndorsedMemo e ON e.servicetypeId = m.servicetypeId
                                       AND e.EndorsedStatus = 4
LEFT JOIN dbo.v_FullyPaymentedMemo p ON p.servicetypeId = m.servicetypeId
                                        AND p.PaymentStatus = 4
GROUP BY m.MonthId, m.YearMemo, m.MonthMemo, m.FormCode, m.FormName, ae.ResultType, ae.ActualExpense, e.curStatus, p.curStatus';
    DECLARE @Params NVARCHAR(MAX) = N'
									@FromDate DATETIME,
									@ToDate DATETIME ,
									@MemoCode NVARCHAR(4000)';

    --PRINT @sql;
    EXEC sp_executesql @sql, @Params, @FromDate = @FromDate, @ToDate = @ToDate, @MemoCode = @MemoCode;

END;
GO