
/****** Object:  StoredProcedure [fwd].[GetCustomerNotHaveRequestTicketByJobList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [fwd].[GetCustomerNotHaveRequestTicketByJobList]

	@CustomerIdList NVARCHAR(MAX),
	@ServiceTypeId UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	dbo. ConvertToGuid(StringPart, '00000000-0000-0000-0000-000000000000') CustomerId
	INTO	#TempCustomer
	FROM	dbo.SplitString(@CustomerIdList, ';')

	SELECT	c.CustomerId
	FROM	#TempCustomer c WITH(NOLOCK)
			LEFT JOIN dbo.RequestTicket rt WITH(NOLOCK) ON rt.CustomerId = c.CustomerId AND rt.ServiceTypeId = @ServiceTypeId AND rt.Deleted = 0
	WHERE	rt.Id IS NULL

END
GO