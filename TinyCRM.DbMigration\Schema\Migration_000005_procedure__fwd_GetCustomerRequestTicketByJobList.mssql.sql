
/****** Object:  StoredProcedure [fwd].[GetCustomerRequestTicketByJobList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetCustomerRequestTicketByJobList]

	@JobList dbo.stringList READONLY,
	@ServiceTypeId UNIQUEIDENTIFIER,
	@SelectString NVARCHAR(MAX)

AS
BEGIN

	DECLARE @ExecutedString NVARCHAR(MAX) = N'
	SELECT	c.Id, c.Code, c.Job, rt.Id RequestTicketId' + ISNULL(@SelectString,'') + '
	FROM	dbo.Customer c
			JOIN @JobList job ON c.Job = job.Value
			LEFT JOIN dbo.RequestTicket rt ON rt.CustomerId = c.Id AND rt.ServiceTypeId = @ServiceTypeId AND rt.Deleted = 0
	WHERE	c.Deleted = 0 AND c.IsDisabled = 0 '

	DECLARE @ParamDefs NVARCHAR(MAX) = N'@JobList dbo.stringList READONLY,
										@ServiceTypeId UNIQUEIDENTIFIER'

	PRINT @ExecutedString

	EXECUTE sp_executesql @ExecutedString, @ParamDefs, @JobList = @JobList, @ServiceTypeId = @ServiceTypeId

END
GO