
/****** Object:  StoredProcedure [fwd].[GetEndorseDefinitionReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetEndorseDefinitionReport]
    @FromDate DATETIME, @ToDate DATETIME, @PaymentType UNIQUEIDENTIFIER, @ContestCode NVARCHAR(1000), @TicketCode NVARCHAR(1000)
AS
BEGIN
    IF @FromDate IS NULL
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, [dbo].[Pop_GetDate]()), DATEPART(MONTH, [dbo].[Pop_GetDate]()), 1, 0, 0, 0, 0);
    ELSE
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, @FromDate), DATEPART(MONTH, @FromDate), DATEPART(DAY, @FromDate), 0, 0, 0, 0);
    IF @ToDate IS NULL
        SET @ToDate = DATEADD(s, -1, DATEADD(mm, DATEDIFF(m, 0, [dbo].[Pop_GetDate]()) + 1, 0));
    ELSE
        SET @ToDate = DATETIMEFROMPARTS(DATEPART(YEAR, @ToDate), DATEPART(MONTH, @ToDate), DATEPART(DAY, @ToDate), 23, 59, 59, 0);

    SELECT CONVERT(DATETIME, DefaultValue, 103) StartTime, DynamicFormId
    INTO #StartMemo
    FROM dbo.DynamicFieldDefinition
    WHERE Name = 'ThoiHanBatDat'
          AND Deleted = 0
          AND DefaultValue IS NOT NULL;

    SELECT YEAR(dfdtime.StartTime) AS Year, MONTH(dfdtime.StartTime) AS Month, df.Code MemoCode, df.Name AS MemoName, rt.Code AS TicketCode,
           CASE WHEN ex.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Normal/ER' ELSE 'Appeal/AR' END AS TypeEndorse,
           REPLACE(REPLACE(dfd.DefaultValue, '[', ''), ']', '') AS Designnation, ISNULL(cash.Amount, 0) AS Cash, ISNULL(noncash.Amount, 0) AS NonCash, ISNULL(br.Name, rt.Status) AS MemoResult
    FROM dbo.RequestTicket rt
    JOIN fwd.ExpenseAndAdjustmentHistory hs ON hs.RequestTicketId = rt.Id
    JOIN dbo.ExpenseItem ex ON ex.Id = hs.ExpenseItemId
    JOIN dbo.ServiceType st ON st.Id = ex.GroupCauseOfExpenseId
    JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
    JOIN #StartMemo dfdtime ON dfdtime.DynamicFormId = df.Id
    LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = dfdtime.DynamicFormId
                                                AND dfd.Name = 'DoiTuongThamDu'
                                                AND dfd.Deleted = 0
    LEFT JOIN dbo.BusinessResult br ON rt.TicketBusinessResultId = br.Id
    LEFT JOIN (   SELECT SUM(expen1.Amount) Amount, expen1.EndorsementContestId, expen1.EndorsementId
                  FROM ExpenseItem expen1
                  WHERE expen1.PaymentType = '8B40EE76-4304-466D-920F-11C1D8A55415'
                        AND expen1.EndorsementId IS NOT NULL
                  GROUP BY expen1.EndorsementContestId, expen1.EndorsementId) cash ON cash.EndorsementContestId = ex.EndorsementContestId
                                                                                      AND cash.EndorsementId = ex.EndorsementId
    LEFT JOIN (   SELECT SUM(expen2.Amount) Amount, expen2.EndorsementContestId, expen2.EndorsementId
                  FROM ExpenseItem expen2
                  WHERE expen2.PaymentType <> '8B40EE76-4304-466D-920F-11C1D8A55415'
                        AND expen2.EndorsementId IS NOT NULL
                  GROUP BY expen2.EndorsementContestId, expen2.EndorsementId) noncash ON noncash.EndorsementContestId = ex.EndorsementContestId
                                                                                         AND noncash.EndorsementId = ex.EndorsementId
    WHERE (rt.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' AND ex.Deleted = 0)
          AND (dfdtime.StartTime BETWEEN @FromDate AND @ToDate)
          AND (@ContestCode IS NULL OR (@ContestCode IS NOT NULL AND df.Code COLLATE Latin1_General_CI_AI LIKE '%' + @ContestCode + '%' COLLATE Latin1_General_CI_AI))
          AND (@TicketCode IS NULL OR (@TicketCode IS NOT NULL AND rt.Code COLLATE Latin1_General_CI_AI LIKE '%' + @TicketCode + '%' COLLATE Latin1_General_CI_AI))
          AND (@PaymentType IS NULL OR (@PaymentType IS NOT NULL AND ex.PaymentType = @PaymentType))
    GROUP BY rt.Code, df.Code, df.Name, ex.ServiceTypeId, br.Name, cash.Amount, noncash.Amount, dfd.DefaultValue, dfdtime.StartTime, rt.Status
    UNION
    -------- chot chi phi chua endorse
    SELECT YEAR(dfdtime.StartTime) AS Year, MONTH(dfdtime.StartTime) AS Month, df.Code MemoCode, df.Name AS MemoName, NULL AS TicketCode,
           CASE WHEN ex.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Normal/ER' ELSE 'Appeal/AR' END AS TypeEndorse,
           REPLACE(REPLACE(dfd.DefaultValue, '[', ''), ']', '') AS Designnation, ISNULL(cash.Amount, 0) AS Cash, ISNULL(noncash.Amount, 0) AS NonCash, NULL AS MemoResult
    FROM dbo.ExpenseItem ex
    JOIN dbo.ServiceType st ON st.Id = ex.GroupCauseOfExpenseId
    JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
    JOIN #StartMemo dfdtime ON dfdtime.DynamicFormId = df.Id
    LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = dfdtime.DynamicFormId
                                                AND dfd.Name = 'DoiTuongThamDu'
                                                AND dfd.Deleted = 0
    LEFT JOIN (   SELECT SUM(expen1.Amount) Amount, expen1.GroupCauseOfExpenseId
                  FROM ExpenseItem expen1
                  WHERE expen1.PaymentType = '8B40EE76-4304-466D-920F-11C1D8A55415'
                        AND expen1.EndorsementId IS NULL
                  GROUP BY expen1.GroupCauseOfExpenseId) cash ON cash.GroupCauseOfExpenseId = ex.GroupCauseOfExpenseId
    LEFT JOIN (   SELECT SUM(expen2.Amount) Amount, expen2.GroupCauseOfExpenseId
                  FROM ExpenseItem expen2
                  WHERE expen2.PaymentType <> '8B40EE76-4304-466D-920F-11C1D8A55415'
                        AND expen2.EndorsementId IS NULL
                  GROUP BY expen2.GroupCauseOfExpenseId) noncash ON noncash.GroupCauseOfExpenseId = ex.GroupCauseOfExpenseId
    WHERE (ex.Deleted = 0 AND ex.EndorsementId IS NULL)
          AND (dfdtime.StartTime BETWEEN @FromDate AND @ToDate)
          AND (@ContestCode IS NULL OR (@ContestCode IS NOT NULL AND df.Code COLLATE Latin1_General_CI_AI LIKE '%' + @ContestCode + '%' COLLATE Latin1_General_CI_AI))
          AND (@PaymentType IS NULL OR (@PaymentType IS NOT NULL AND ex.PaymentType = @PaymentType))
    GROUP BY df.Code, df.Name, ex.ServiceTypeId, cash.Amount, noncash.Amount, dfd.DefaultValue, dfdtime.StartTime
    UNION
    --------  Appeal
    SELECT YEAR(dfdtime.StartTime) AS Year, MONTH(dfdtime.StartTime) AS Month, df.Code MemoCode, df.Name AS MemoName, rt.Code AS TicketCode,
           CASE WHEN adj.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Normal/ER' ELSE 'Appeal/AR' END AS TypeEndorse,
           REPLACE(REPLACE(dfd.DefaultValue, '[', ''), ']', '') AS Designnation, ISNULL(cash.Amount, 0) AS Cash, ISNULL(noncash.Amount, 0) AS NonCash, ISNULL(br.Name, rt.Status) AS MemoResult
    FROM dbo.RequestTicket rt
    JOIN fwd.ExpenseAndAdjustmentHistory hs ON hs.RequestTicketId = rt.Id
    JOIN dbo.PaymentAdjustment adj ON adj.Id = hs.AdjustmentId
    JOIN dbo.ServiceType st ON st.Id = adj.ContestId
    JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
    JOIN #StartMemo dfdtime ON dfdtime.DynamicFormId = df.Id
    LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = dfdtime.DynamicFormId
                                                AND dfd.Name = 'DoiTuongThamDu'
                                                AND dfd.Deleted = 0
    LEFT JOIN dbo.BusinessResult br ON rt.TicketBusinessResultId = br.Id
    LEFT JOIN (   SELECT SUM(adj1.Amount) Amount, adj1.ContestId, adj1.EndorsementId
                  FROM PaymentAdjustment adj1
                  WHERE adj1.PaymentType = '8B40EE76-4304-466D-920F-11C1D8A55415'
                  GROUP BY adj1.ContestId, adj1.EndorsementId) cash ON cash.ContestId = adj.ContestId
                                                                       AND cash.EndorsementId = adj.EndorsementId
    LEFT JOIN (   SELECT SUM(adj2.Amount) Amount, adj2.ContestId, adj2.EndorsementId
                  FROM PaymentAdjustment adj2
                  WHERE adj2.PaymentType <> '8B40EE76-4304-466D-920F-11C1D8A55415'
                  GROUP BY adj2.ContestId, adj2.EndorsementId) noncash ON noncash.ContestId = adj.ContestId
                                                                          AND noncash.EndorsementId = adj.EndorsementId
    WHERE (rt.ServiceTypeId = 'AF9F94C9-BC33-475B-A45D-D470E0438C35' AND adj.Deleted = 0)
          AND (dfdtime.StartTime BETWEEN @FromDate AND @ToDate)
          AND (@ContestCode IS NULL OR (@ContestCode IS NOT NULL AND df.Code COLLATE Latin1_General_CI_AI LIKE '%' + @ContestCode + '%' COLLATE Latin1_General_CI_AI))
          AND (@TicketCode IS NULL OR (@TicketCode IS NOT NULL AND rt.Code COLLATE Latin1_General_CI_AI LIKE '%' + @TicketCode + '%' COLLATE Latin1_General_CI_AI))
          AND (@PaymentType IS NULL OR (@PaymentType IS NOT NULL AND adj.PaymentType = @PaymentType))
    GROUP BY rt.Code, df.Code, df.Name, adj.ServiceTypeId, br.Name, cash.Amount, noncash.Amount, dfd.DefaultValue, dfdtime.StartTime, rt.Status;
END;
GO