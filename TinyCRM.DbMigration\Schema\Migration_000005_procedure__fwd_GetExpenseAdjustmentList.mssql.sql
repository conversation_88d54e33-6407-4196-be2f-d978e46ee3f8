
/****** Object:  StoredProcedure [fwd].[GetExpenseAdjustmentList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [fwd].[GetExpenseAdjustmentList]
	@DynamicFieldValueId UNIQUEIDENTIFIER
AS
BEGIN
	DECLARE @RequestTicketId UNIQUEIDENTIFIER = NULL;
	DECLARE @ServiceTypeId UNIQUEIDENTIFIER = NULL;
	DECLARE @ConditionCase BigInt = 0;
	SELECT @RequestTicketId = rt.Id, @ServiceTypeId = rt.ServiceTypeId FROM dbo.DynamicFieldValue dfv
	JOIN dbo.DynamicFormValue df ON df.Id = dfv.DynamicFormValueId
	JOIN dbo.RequestTicket rt ON rt.DynamicFormValueId = df.Id
	WHERE dfv.Id = @DynamicFieldValueId

	SELECT @ConditionCase = REPLACE(dfv.Value, '.', '')
	FROM	dbo.RequestTicket rt
			JOIN dbo.DynamicFieldValue dfv ON dfv.DynamicFormValueId = rt.DynamicFormValueId
			JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
	WHERE	rt.Id = @RequestTicketId AND dfd.Name = 'So_tien_gioi_han_Mot_case'
	;WITH excel as(
		SELECT exaj.Id AdjustmentContestId, temp.* FROM fwd.ExpenseAdjustmentImportSession  exaj
		JOIN (
			SELECT st.Id ContestId,df.Name, df.Code,COUNT(pa.Amount) ExcelCount, SUM(pa.Amount) ExcelAmount, SUM(temp.BigCase) TotalBigCase, hs.FileContestId, pa.Status FROM fwd.ExpenseAndAdjustmentHistory hs
			JOIN dbo.PaymentAdjustment pa ON pa.Id = hs.AdjustmentId
			JOIN dbo.ServiceType st ON st.Id = pa.ContestId
			JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
			JOIN (
			SELECT pa.ContestId, pa.AgencyId ,case WHEN  SUM(pa.Amount) > @ConditionCase THEN 1 ELSE 0 END BigCase FROM dbo.PaymentAdjustment pa WHERE pa.Status = 1 GROUP BY pa.ContestId, pa.AgencyId 
			) temp ON temp.ContestId = pa.ContestId AND temp.AgencyId = pa.AgencyId
			WHERE hs.RequestTicketId = @RequestTicketId
			GROUP BY df.Name, df.Code, st.Id, hs.FileContestId, pa.Status
		) temp ON temp.ContestId = exaj.ContestId AND temp.FileContestId = exaj.Id
	),

	totalnotexcel AS(
		SELECT excel.ContestId ,excel.Name, excel.Code, SUM(pa.Amount) TotalAmount FROM excel 
		JOIN dbo.PaymentAdjustment pa ON pa.ContestId = excel.ContestId
		WHERE (pa.EndorsementId <>  @DynamicFieldValueId OR pa.PaymentId <> @DynamicFieldValueId)  AND pa.Status = 1 AND pa.ServiceTypeId = @ServiceTypeId
		GROUP BY excel.Name, excel.Code, excel.ContestId
	)

	SELECT excel.ContestId, excel.Name ContestName, excel.Code ContestCode, excel.ExcelCount, excel.ExcelAmount, excel.AdjustmentContestId ,TotalAmount = 
	CASE WHEN totalnotexcel.TotalAmount IS NULL THEN IIF(excel.Status = 1, excel.ExcelAmount, 0) ELSE IIF(excel.Status = 1, excel.ExcelAmount + totalnotexcel.TotalAmount, totalnotexcel.TotalAmount)   END
	, excel.TotalBigCase FROM  excel
	LEFT JOIN totalnotexcel ON totalnotexcel.ContestId = excel.ContestId

END
GO