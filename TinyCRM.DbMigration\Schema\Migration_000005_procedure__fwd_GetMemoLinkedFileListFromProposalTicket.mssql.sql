
/****** Object:  StoredProcedure [fwd].[GetMemoLinkedFileListFromProposalTicket]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetMemoLinkedFileListFromProposalTicket]

	@DynamicFormValueId UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	f.Id, f.FileName, f.Extension, f.ReferenceObjectId, f.ReferenceObjectType, f.CreatedBy, f.CreatedDate
	FROM	dbo.DynamicFormValue dfrv
			JOIN dbo.RequestTicket rt ON rt.DynamicFormValueId = dfrv.Id
			JOIN dbo.EntityLink ticketEL ON ticketEL.FromEntityId = rt.Id
			JOIN dbo.ServiceType st ON st.Id = ticketEL.ToEntityId
			JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
			JOIN dbo.EntityLink el ON df.Id = el.FromEntityId
			JOIN dbo.Campaign c ON c.Id = el.ToEntityId
			JOIN dbo.[File] f ON f.Id = c.CampaignFileId
	WHERE	dfrv.Id = @DynamicFormValueId
			AND f.Deleted = 0

END
GO