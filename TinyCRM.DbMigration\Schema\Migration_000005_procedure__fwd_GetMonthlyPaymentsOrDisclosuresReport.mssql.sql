
/****** Object:  StoredProcedure [fwd].[GetMonthlyPaymentsOrDisclosuresReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetMonthlyPaymentsOrDisclosuresReport]
    @FromDate DATETIME, @ToDate DATETIME
AS
BEGIN
	DECLARE @TicketBusinessResultId UNIQUEIDENTIFIER = '11111111-1111-1111-1111-111111111111' ---- is approved
    SET @fromDate = DATETIMEFROMPARTS(DATEPART(YEAR, @fromDate), DATEPART(MONTH, @fromDate), DATEPART(DAY, @fromDate), 0, 0, 0, 0);
    SET @toDate = DATETIMEFROMPARTS(DATEPART(YEAR, @toDate), DATEPART(MONTH, @toDate), DATEPART(DAY, @toDate), 23, 59, 59, 0);
    ;WITH report
    AS (
		--endorse data info
        SELECT rt.Id, 'Normal' ResultType, SUM(ei.Amount) Amount, COUNT(ei.Id) Qualifiers, rt.FinishedTicketDate, i.Name PaymentType, ei.GroupCauseOfExpenseId
        FROM dbo.RequestTicket rt
        JOIN dbo.ServiceType st ON st.Id = rt.ServiceTypeId
        JOIN fwd.ExpenseAndAdjustmentHistory h ON h.RequestTicketId = rt.Id AND rt.Deleted = 0
        JOIN dbo.ExpenseItem ei ON ei.Id = h.ExpenseItemId AND ei.Deleted = 0
        JOIN dbo.InfoList i ON i.Id = ei.PaymentType
        WHERE h.Type = 1
              AND st.Id = '9C468B2E-3996-44E3-B98D-D40B8D029038'
              AND rt.CreatedDate >= @fromDate
              AND rt.CreatedDate <= @toDate
			  AND rt.Status = 4 AND rt.TicketBusinessResultId = @TicketBusinessResultId
        GROUP BY rt.Id, rt.FinishedTicketDate, i.Name , ei.GroupCauseOfExpenseId
        UNION ALL
		--appeal data info
        SELECT rt.Id, 'Appeal' ResultType, SUM(a.Amount) Amount, COUNT(a.Id) Qualifiers, rt.FinishedTicketDate, i.Name PaymentType, a.ContestId GroupCauseOfExpenseId
        FROM dbo.RequestTicket rt
        JOIN dbo.ServiceType st ON st.Id = rt.ServiceTypeId
        JOIN fwd.ExpenseAndAdjustmentHistory h ON h.RequestTicketId = rt.Id  AND rt.Deleted = 0
        JOIN dbo.PaymentAdjustment a ON a.Id = h.AdjustmentId AND a.Deleted = 0
        JOIN dbo.InfoList i ON i.Id = a.PaymentType
        WHERE st.Id = 'AF9F94C9-BC33-475B-A45D-D470E0438C35'
              AND rt.CreatedDate >= @fromDate
              AND rt.CreatedDate <= @toDate
			  AND rt.Status = 4 AND rt.TicketBusinessResultId = @TicketBusinessResultId
        GROUP BY rt.Id, rt.FinishedTicketDate, i.Name , a.ContestId 
    )
    SELECT   df.Code ContestCode, df.Name ContestName,r.ResultType,r.PaymentType,r.Qualifiers,r.Amount,r.FinishedTicketDate,ns.Value BudgetName,b.Value BudgetYear--,df.Id ContestId,ddtcv.Value,r.Id ticketId,r.GroupCauseOfExpenseId
    FROM report r
    JOIN dbo.ServiceType st ON st.Id = r.GroupCauseOfExpenseId
    JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
	JOIN (SELECT * FROM  dbo.DynamicDefinedTableCellValue WHERE DynamicDefinedTableColumnId = '3BDD1AA9-AAAB-4432-878E-45D9F3D0DA47' AND TRIM(Value) <>'' ) ddtcv ON    df.Id = CAST(ddtcv.Value AS UNIQUEIDENTIFIER)
	JOIN dbo.DynamicDefinedTableCellValue ns ON ns.DynamicFieldValueId = ddtcv.DynamicFieldValueId AND ns.RowNumber = ddtcv.RowNumber AND ns.DynamicDefinedTableColumnId = 'DE6F12E7-7F51-41A8-8CF6-44547F1A3D82'  -- column ngân sách
	JOIN dbo.DynamicDefinedTableCellValue b ON b.DynamicFieldValueId = ddtcv.DynamicFieldValueId AND b.RowNumber = ddtcv.RowNumber AND b.DynamicDefinedTableColumnId = 'F3587036-4CFF-4729-A429-7ABAD2AF2113' -- column ngân sách năm
  
END;
GO