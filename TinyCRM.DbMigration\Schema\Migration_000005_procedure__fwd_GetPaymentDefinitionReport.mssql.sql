
/****** Object:  StoredProcedure [fwd].[GetPaymentDefinitionReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetPaymentDefinitionReport]
    @FromDate DATETIME, @ToDate DATETIME, @PaymentType UNIQUEIDENTIFIER, @ContestCode NVARCHAR(1000), @TicketCode NVARCHAR(1000)
AS
BEGIN
    IF @FromDate IS NULL
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, [dbo].[Pop_GetDate]()), DATEPART(MONTH, [dbo].[Pop_GetDate]()), 1, 0, 0, 0, 0);
    ELSE
        SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, @FromDate), DATEPART(MONTH, @FromDate), DATEPART(DAY, @FromDate), 0, 0, 0, 0);
    IF @ToDate IS NULL
        SET @ToDate = DATEADD(s, -1, DATEADD(mm, DATEDIFF(m, 0, [dbo].[Pop_GetDate]()) + 1, 0));
    ELSE
        SET @ToDate = DATETIMEFROMPARTS(DATEPART(YEAR, @ToDate), DATEPART(MONTH, @ToDate), DATEPART(DAY, @ToDate), 23, 59, 59, 0);

    SELECT CONVERT(DATETIME, DefaultValue, 103) StartTime, DynamicFormId
    INTO #StartMemo
    FROM dbo.DynamicFieldDefinition
    WHERE Name = 'ThoiHanBatDat'
          AND Deleted = 0
          AND DefaultValue IS NOT NULL;
    WITH endorse
    AS (
        SELECT YEAR(dfdtime.StartTime) AS Year, MONTH(dfdtime.StartTime) AS Month, df.Code MemoCode, df.Name AS MemoName, rt.Code AS TicketCode,
               CASE WHEN ex.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Normal/ER' ELSE 'Appeal/AR' END AS TypeEndorse, ISNULL(cash.Amount, 0) AS Cash,
               ISNULL(noncash.Amount, 0) AS NonCash, ISNULL(cash.Amount, 0) + ISNULL(noncash.Amount, 0) TotalRemain, br.Name TicketResult, st.Id
        FROM dbo.RequestTicket rt
        JOIN fwd.ExpenseAndAdjustmentHistory hs ON hs.RequestTicketId = rt.Id
        JOIN dbo.ExpenseItem ex ON ex.Id = hs.ExpenseItemId
        JOIN dbo.ServiceType st ON st.Id = ex.GroupCauseOfExpenseId
        JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
        JOIN #StartMemo dfdtime ON dfdtime.DynamicFormId = df.Id
        LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = dfdtime.DynamicFormId
                                                    AND dfd.Name = 'DoiTuongThamDu'
                                                    AND dfd.Deleted = 0
        LEFT JOIN dbo.BusinessResult br ON rt.TicketBusinessResultId = br.Id
        LEFT JOIN (   SELECT SUM(expen1.Amount) Amount, expen1.EndorsementContestId, expen1.EndorsementId
                      FROM ExpenseItem expen1
                      WHERE expen1.PaymentType = '8B40EE76-4304-466D-920F-11C1D8A55415'
                            AND expen1.EndorsementId IS NOT NULL
                      GROUP BY expen1.EndorsementContestId, expen1.EndorsementId) cash ON cash.EndorsementContestId = ex.EndorsementContestId
                                                                                          AND cash.EndorsementId = ex.EndorsementId
        LEFT JOIN (   SELECT SUM(expen2.Amount) Amount, expen2.EndorsementContestId, expen2.EndorsementId
                      FROM ExpenseItem expen2
                      WHERE expen2.PaymentType <> '8B40EE76-4304-466D-920F-11C1D8A55415'
                            AND expen2.EndorsementId IS NOT NULL
                      GROUP BY expen2.EndorsementContestId, expen2.EndorsementId) noncash ON noncash.EndorsementContestId = ex.EndorsementContestId
                                                                                             AND noncash.EndorsementId = ex.EndorsementId
        WHERE (rt.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' AND ex.Deleted = 0)
              AND (dfdtime.StartTime BETWEEN @FromDate AND @ToDate)
              AND (@ContestCode IS NULL OR (@ContestCode IS NOT NULL AND df.Code COLLATE Latin1_General_CI_AI LIKE '%' + @ContestCode + '%' COLLATE Latin1_General_CI_AI))
              AND (@TicketCode IS NULL OR (@TicketCode IS NOT NULL AND rt.Code COLLATE Latin1_General_CI_AI LIKE '%' + @TicketCode + '%' COLLATE Latin1_General_CI_AI))
              AND (@PaymentType IS NULL OR (@PaymentType IS NOT NULL AND ex.PaymentType = @PaymentType))
        GROUP BY rt.Code, dfdtime.StartTime, df.Code, df.Name, ex.ServiceTypeId, br.Name, cash.Amount, noncash.Amount, st.Id
    ),   payment
    AS (
        SELECT YEAR(dfdtime.StartTime) AS Year, MONTH(dfdtime.StartTime) AS Month, df.Code MemoCode, df.Name AS MemoName, rt.Code AS TicketCode,
               CASE WHEN ex.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Normal/ER' ELSE 'Appeal/AR' END AS TypeEndorse,
               REPLACE(REPLACE(dfd.DefaultValue, '[', ''), ']', '') AS Designnation, NULL AS WriteOffAmount, ISNULL(cash.Amount, 0) AS Cash, ISNULL(noncash.Amount, 0) AS NonCash,
               CASE WHEN br.Id = '11111111-1111-1111-1111-111111111111' THEN ISNULL(cash.Amount, 0) + ISNULL(noncash.Amount, 0)ELSE 0 END AS TotalPayment, br.Name TicketResult, st.Id
        FROM dbo.RequestTicket rt
        JOIN fwd.ExpenseAndAdjustmentHistory hs ON hs.RequestTicketId = rt.Id
        JOIN dbo.ExpenseItem ex ON ex.Id = hs.ExpenseItemId
        JOIN dbo.ServiceType st ON st.Id = ex.GroupCauseOfExpenseId
        JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
        JOIN #StartMemo dfdtime ON dfdtime.DynamicFormId = df.Id
        LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = dfdtime.DynamicFormId
                                                    AND dfd.Name = 'DoiTuongThamDu'
                                                    AND dfd.Deleted = 0
        LEFT JOIN dbo.BusinessResult br ON rt.TicketBusinessResultId = br.Id
        LEFT JOIN (   SELECT SUM(expen1.Amount) Amount, expen1.EndorsementContestId, expen1.EndorsementId
                      FROM ExpenseItem expen1
                      WHERE expen1.PaymentType = '8B40EE76-4304-466D-920F-11C1D8A55415'
                            AND expen1.PaymentId IS NOT NULL
                      GROUP BY expen1.EndorsementContestId, expen1.EndorsementId) cash ON cash.EndorsementContestId = ex.EndorsementContestId
                                                                                          AND cash.EndorsementId = ex.EndorsementId
        LEFT JOIN (   SELECT SUM(expen2.Amount) Amount, expen2.EndorsementContestId, expen2.EndorsementId
                      FROM ExpenseItem expen2
                      WHERE expen2.PaymentType <> '8B40EE76-4304-466D-920F-11C1D8A55415'
                            AND expen2.PaymentId IS NOT NULL
                      GROUP BY expen2.EndorsementContestId, expen2.EndorsementId) noncash ON noncash.EndorsementContestId = ex.EndorsementContestId
                                                                                             AND noncash.EndorsementId = ex.EndorsementId
        WHERE (rt.ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' AND ex.Deleted = 0)
              AND (dfdtime.StartTime BETWEEN @FromDate AND @ToDate)
              AND (@ContestCode IS NULL OR (@ContestCode IS NOT NULL AND df.Code COLLATE Latin1_General_CI_AI LIKE '%' + @ContestCode + '%' COLLATE Latin1_General_CI_AI))
              AND (@TicketCode IS NULL OR (@TicketCode IS NOT NULL AND rt.Code COLLATE Latin1_General_CI_AI LIKE '%' + @TicketCode + '%' COLLATE Latin1_General_CI_AI))
              AND (@PaymentType IS NULL OR (@PaymentType IS NOT NULL AND ex.PaymentType = @PaymentType))
        GROUP BY rt.Code, dfdtime.StartTime, df.Code, df.Name, ex.ServiceTypeId, br.Name, cash.Amount, noncash.Amount, st.Id, br.Id, dfd.DefaultValue
    ),   appeal
    AS (
        SELECT YEAR(dfdtime.StartTime) AS Year, MONTH(dfdtime.StartTime) AS Month, df.Code MemoCode, df.Name AS MemoName, rt.Code AS TicketCode,
               CASE WHEN adj.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Normal/ER' ELSE 'Appeal/AR' END AS TypeEndorse,
               ISNULL(Total.Amount, 0) - ISNULL(AllPayment.Amount, 0) TotalRemain, CASE WHEN adj.PaymentType = '8B40EE76-4304-466D-920F-11C1D8A55415' THEN SUM(adj.Amount)ELSE 0 END Cash,
               CASE WHEN adj.PaymentType <> '8B40EE76-4304-466D-920F-11C1D8A55415' THEN SUM(adj.Amount)ELSE 0 END NonCash, br.Name TicketResult, NULL AS WriteOffAmount,
               REPLACE(REPLACE(dfd.DefaultValue, '[', ''), ']', '') AS Designnation, CASE WHEN br.Id = '11111111-1111-1111-1111-111111111111' THEN ISNULL(SUM(adj.Amount), 0)ELSE 0 END Paymented
        FROM dbo.RequestTicket rt
        JOIN fwd.ExpenseAndAdjustmentHistory hs ON hs.RequestTicketId = rt.Id
        JOIN dbo.PaymentAdjustment adj ON adj.Id = hs.AdjustmentId
        JOIN dbo.ServiceType st ON st.Id = adj.ContestId
        JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
        JOIN #StartMemo dfdtime ON dfdtime.DynamicFormId = df.Id
        LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = dfdtime.DynamicFormId
                                                    AND dfd.Name = 'DoiTuongThamDu'
                                                    AND dfd.Deleted = 0
        LEFT JOIN dbo.BusinessResult br ON rt.TicketBusinessResultId = br.Id
        JOIN (SELECT ContestId, SUM(Amount) Amount FROM dbo.PaymentAdjustment GROUP BY ContestId) Total ON Total.ContestId = adj.ContestId
        JOIN (   SELECT ContestId, SUM(pa.Amount) Amount
                 FROM dbo.PaymentAdjustment pa
                 JOIN fwd.ExpenseAndAdjustmentHistory hs ON hs.AdjustmentId = pa.Id
                 JOIN dbo.RequestTicket rt ON hs.RequestTicketId = rt.Id
                 JOIN dbo.BusinessResult br ON rt.TicketBusinessResultId = br.Id
                 WHERE hs.Type = 2
                 GROUP BY ContestId) AllPayment ON AllPayment.ContestId = adj.ContestId
        WHERE (rt.ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' AND adj.Deleted = 0)
              AND (dfdtime.StartTime BETWEEN @FromDate AND @ToDate)
              AND (@ContestCode IS NULL OR (@ContestCode IS NOT NULL AND df.Code COLLATE Latin1_General_CI_AI LIKE '%' + @ContestCode + '%' COLLATE Latin1_General_CI_AI))
              AND (@TicketCode IS NULL OR (@TicketCode IS NOT NULL AND rt.Code COLLATE Latin1_General_CI_AI LIKE '%' + @TicketCode + '%' COLLATE Latin1_General_CI_AI))
              AND (@PaymentType IS NULL OR (@PaymentType IS NOT NULL AND adj.PaymentType = @PaymentType))
        GROUP BY rt.Code, dfdtime.StartTime, df.Code, df.Name, adj.ServiceTypeId, br.Name, adj.PaymentType, Total.ContestId, Total.Amount, br.Id, AllPayment.Amount, dfd.DefaultValue
    )
    SELECT endorse.Year, endorse.Month, endorse.MemoCode, endorse.MemoName, endorse.TicketCode, endorse.TypeEndorse, endorse.TotalRemain - ISNULL(payment.TotalPayment, 0) TotalRemain,
           ISNULL(payment.Cash, 0) Cash, ISNULL(payment.NonCash, 0) NonCash, payment.TicketResult, payment.WriteOffAmount, payment.Designnation, ISNULL(payment.TotalPayment, 0) Paymented
    FROM payment
    JOIN endorse ON payment.Id = endorse.Id
    UNION
    SELECT * FROM appeal;
END;
GO