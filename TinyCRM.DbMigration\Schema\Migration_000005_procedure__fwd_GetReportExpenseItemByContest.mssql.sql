
/****** Object:  StoredProcedure [fwd].[GetReportExpenseItemByContest]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

----Update store
CREATE PROCEDURE [fwd].[GetReportExpenseItemByContest]
	@GroupCauseOfExpenseId UNIQUEIDENTIFIER,
	@EndorsementImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
	SELECT df.Name ContestName, df.Code ContestCode, c.Code AgencyCode, c.Name AgencyName,
	temp.Name FieldName, ei.Amount, ifl.Name PaymentType, 'Endorsement' Type
		FROM  dbo.ExpenseItem ei
		JOIN dbo.ServiceType st ON st.Id = ei.GroupCauseOfExpenseId
		JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
		JOIN dbo.InfoList ifl ON ifl.Id = ei.PaymentType
		JOIN dbo.Customer c ON c.Id = ei.PayeeId
		JOIN (
			SELECT dfv.Id DynamicFieldValueId, dfd.Name, ei.Id ExpenseId FROM dbo.ExpenseItem ei
			JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
			JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
		) temp ON temp.ExpenseId = ei.Id
	WHERE ei.GroupCauseOfExpenseId = @GroupCauseOfExpenseId 
	And (@EndorsementImportSessionId is null or ei.EndorsementContestId = @EndorsementImportSessionId)

END
GO