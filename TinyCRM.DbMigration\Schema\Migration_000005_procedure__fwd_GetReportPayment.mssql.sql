
/****** Object:  StoredProcedure [fwd].[GetReportPayment]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [fwd].[GetReportPayment]
			@StartRow INT,
	@EndRow INT
AS
BEGIN
	WITH totaladjsut as(
		SELECT ContestId, SUM(Amount) AdjAmount, COUNT(Amount) AdjQualifier FROM dbo.PaymentAdjustment WHERE  Status = 1 AND Deleted = 0 GROUP BY ContestId
	),	
	adjustendorse as(
		SELECT ContestId, SUM(CASE WHEN EndorsementId IS NULL THEN 0 ELSE Amount END ) AdjAmount, Sum(CASE WHEN EndorsementId IS NULL THEN 0 ELSE 1 END) AdjQualifier FROM dbo.PaymentAdjustment WHERE Status = 1 AND Deleted = 0 GROUP BY ContestId
	),
	adjustpayment as(
		SELECT ContestId, SUM(CASE WHEN PaymentId IS NULL THEN 0 ELSE Amount END ) AdjAmount, Sum(CASE WHEN PaymentId IS NULL THEN 0 ELSE 1 END) AdjQualifier FROM dbo.PaymentAdjustment WHERE Status = 1 AND Deleted = 0 GROUP BY ContestId
	),
	temp_total as(
		SELECT df.Name ContestName, dff.DefaultValue Status, df.Code ContestCode,SUM(exi.Amount) CacAmount, COUNT(exi.Amount) CacQualifier, exi.GroupCauseOfExpenseId ContestId
		FROM dbo.ExpenseItem exi 
		LEFT JOIN dbo.ServiceType st ON st.Id = exi.GroupCauseOfExpenseId
		LEFT JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
		LEFT JOIN dbo.DynamicFieldDefinition dff ON dff.DynamicFormId = df.Id
		LEFT JOIN totaladjsut ON totaladjsut.ContestId = exi.GroupCauseOfExpenseId
		WHERE dff.Name = 'TrangThaiThiDua' AND exi.Deleted = 0
		GROUP BY exi.GroupCauseOfExpenseId, dff.DefaultValue, df.Name, df.Code
	),
	total AS(
		SELECT temp_total.ContestName, temp_total.Status, temp_total.ContestCode, temp_total.CacAmount, temp_total.CacQualifier, temp_total.ContestId, totaladjsut.AdjAmount, totaladjsut.AdjQualifier FROM temp_total
		LEFT JOIN totaladjsut ON temp_total.ContestId = totaladjsut.ContestId
	),
	temp_hasEndore AS(
		SELECT SUM(CASE WHEN exi.EndorsementId IS NULL THEN 0 ELSE exi.Amount END) ExAmount, Sum(CASE WHEN exi.EndorsementId IS NULL THEN 0 ELSE 1 END) ExQualifier, exi.GroupCauseOfExpenseId ContestId
		FROM dbo.ExpenseItem exi 
		LEFT JOIN dbo.ServiceType st ON st.Id = exi.GroupCauseOfExpenseId
		LEFT JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
		LEFT JOIN dbo.DynamicFieldDefinition dff ON dff.DynamicFormId = df.Id
		LEFT JOIN adjustendorse ON adjustendorse.ContestId = exi.GroupCauseOfExpenseId
		WHERE dff.Name = 'TrangThaiThiDua' AND exi.Deleted = 0
		GROUP BY exi.GroupCauseOfExpenseId
	),
	hasEndore AS(
		SELECT temp_hasEndore.ExAmount, temp_hasEndore.ExQualifier, temp_hasEndore.ContestId, adjustendorse.AdjAmount, adjustendorse.AdjQualifier  FROM temp_hasEndore
		LEFT JOIN adjustendorse ON temp_hasEndore.ContestId = adjustendorse.ContestId
	),
	temp_hasPayment AS(
		SELECT SUM(CASE WHEN exi.PaymentId IS NULL THEN 0 ELSE exi.Amount END) ExAmount, Sum(CASE WHEN exi.PaymentId IS NULL THEN 0 ELSE 1 END) ExQualifier, exi.GroupCauseOfExpenseId ContestId
		FROM dbo.ExpenseItem exi 
		LEFT JOIN dbo.ServiceType st ON st.Id = exi.GroupCauseOfExpenseId
		LEFT JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
		LEFT JOIN dbo.DynamicFieldDefinition dff ON dff.DynamicFormId = df.Id
		LEFT JOIN adjustpayment ON adjustpayment.ContestId = exi.GroupCauseOfExpenseId
		WHERE dff.Name = 'TrangThaiThiDua' AND exi.Deleted = 0
		GROUP BY exi.GroupCauseOfExpenseId
	),
	hasPayment as(
		SELECT temp_hasPayment.ExAmount, temp_hasPayment.ExQualifier, temp_hasPayment.ContestId, adjustpayment.AdjAmount, adjustpayment.AdjQualifier  FROM temp_hasPayment
		LEFT JOIN adjustpayment ON temp_hasPayment.ContestId = adjustpayment.ContestId	
	)
	SELECT total.ContestName, total.ContestCode ,total.Status ContestStatus, total.CacAmount ExpenseAmount, total.CacQualifier ExpenseQualifier, total.AdjAmount AdjustmentAmount, total.AdjQualifier AdjustmentQualifier,
	(ISNULL(hasEndore.ExAmount,0) + ISNULL(hasEndore.AdjAmount,0)) EndoreAmount, (ISNULL(hasEndore.ExQualifier,0) + ISNULL(hasEndore.AdjQualifier,0)) EndoreQualifier,
	(CAST((ISNULL(hasEndore.ExQualifier,0) + ISNULL(hasEndore.AdjQualifier,0)) AS FLOAT) / (ISNULL(total.CacQualifier,0) + ISNULL(total.AdjQualifier,0)))*100 EndoreCompleted,
	(ISNULL(hasPayment.ExAmount,0) + ISNULL(hasPayment.AdjAmount,0)) PaymentAmount, (ISNULL(hasPayment.ExQualifier,0) + ISNULL(hasPayment.AdjQualifier,0)) PaymentQualifier,
	(CAST((ISNULL(hasPayment.ExQualifier,0) + ISNULL(hasPayment.AdjQualifier,0)) AS FLOAT) / (ISNULL(total.CacQualifier,0) + ISNULL(total.AdjQualifier,0)))*100 PaymentCompleted
	INTO #temp FROM total 
	LEFT JOIN hasEndore  ON  hasEndore.ContestId = total.ContestId
	LEFT JOIN hasPayment ON hasPayment.ContestId = total.ContestId
	;WITH cte AS
(
	SELECT	*, ROW_NUMBER() OVER (ORDER BY ContestName) RowNumber
	FROM	#temp
)
	SELECT	cte.*, (SELECT COUNT(cte.RowNumber) FROM cte) TotalCount
	FROM	cte
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow
END
GO