
/****** Object:  StoredProcedure [fwd].[GetReportPaymentByContest]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--------
CREATE PROCEDURE [fwd].[GetReportPaymentByContest]
	@ContestCode NVARCHAR(512),
	@ContestName NVARCHAR(512)
AS
BEGIN
	WITH ExpenseEndorse AS( SELECT  exi.Amount, ifl.Name PaymentType, c.Name AgencyName, c.Code AgencyCode, temp.Name FieldName, 
	EndoreseTicketResultCode = CASE WHEN rt.Status = 1 THEN N'Mới tạo' + ISNULL('-' + br.Name, '') WHEN rt.Status = 2 THEN N'Chuyển xử lý' + ISNULL('-' + br.Name, '')
	WHEN rt.Status = 3 THEN N'Đang xử lý' + ISNULL('-' + br.Name, '') WHEN rt.Status = 4 THEN N'Hoàn thành' + ISNULL('-' + br.Name, '') ELSE br.Name End,
	EndorseServiceType = CASE WHEN exi.ServiceTypeId = 'AF9F94C9-BC33-475B-A45D-D470E0438C35' THEN 'Appeal'
	WHEN exi.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Endorsement'
	WHEN exi.ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' THEN 'Payment'
	ELSE NULL END, rt.Code EndoreseTicketCode , FORMAT (rt.CreatedDate, 'dd/MM/yyyy hh:mm') EndoreseTicketCreatedDate, exi.Id ExpenseItemId, FORMAT (exi.CreatedDate, 'dd/MM/yyyy hh:mm') ItemCreatedDate
	FROM dbo.ExpenseItem exi
	JOIN dbo.ServiceType st ON st.Id = exi.GroupCauseOfExpenseId
	JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
	JOIN dbo.InfoList ifl ON ifl.Id = exi.PaymentType
	JOIN dbo.Customer c ON c.Id = exi.PayeeId
	JOIN (
		SELECT dfv.Id DynamicFieldValueId, dfd.Name, ei.Id ExpenseId FROM dbo.ExpenseItem ei
		JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
		JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
	) temp ON temp.ExpenseId = exi.Id
	LEFT JOIN dbo.DynamicFieldValue dfv ON dfv.Id = exi.EndorsementId
	LEFT JOIN dbo.RequestTicket rt ON rt.DynamicFormValueId = dfv.DynamicFormValueId
	LEFT JOIN dbo.BusinessResult br ON br.Id = rt.TicketBusinessResultId
	WHERE df.Code = @ContestCode AND exi.Deleted = 0)
	, ExpensePayment AS( SELECT  exi.Amount, ifl.Name PaymentType, c.Name AgencyName, c.Code AgencyCode, temp.Name FieldName, 
	PaymentTicketResultCode = CASE WHEN rt.Status = 1 THEN N'Mới tạo' + ISNULL('-' + br.Name, '') WHEN rt.Status = 2 THEN N'Chuyển xử lý' + ISNULL('-' + br.Name, '')
	WHEN rt.Status = 3 THEN N'Đang xử lý' + ISNULL('-' + br.Name, '') WHEN rt.Status = 4 THEN N'Hoàn thành' + ISNULL('-' + br.Name, '') ELSE br.Name End,
	EndorseServiceType = CASE WHEN exi.ServiceTypeId = 'AF9F94C9-BC33-475B-A45D-D470E0438C35' THEN 'Appeal'
	WHEN exi.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Endorsement'
	WHEN exi.ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' THEN 'Payment'
	ELSE NULL END, rt.Code PaymentTicketCode , FORMAT (rt.CreatedDate, 'dd/MM/yyyy hh:mm') PaymentTicketCreatedDate, exi.Id ExpenseItemId, FORMAT (exi.CreatedDate, 'dd/MM/yyyy hh:mm') ItemCreatedDate
	FROM dbo.ExpenseItem exi
	JOIN dbo.ServiceType st ON st.Id = exi.GroupCauseOfExpenseId
	JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
	JOIN dbo.InfoList ifl ON ifl.Id = exi.PaymentType
	JOIN dbo.Customer c ON c.Id = exi.PayeeId
	JOIN (
		SELECT dfv.Id DynamicFieldValueId, dfd.Name, ei.Id ExpenseId FROM dbo.ExpenseItem ei
		JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
		JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
	) temp ON temp.ExpenseId = exi.Id
	LEFT JOIN dbo.DynamicFieldValue dfv ON dfv.Id = exi.PaymentId
	LEFT JOIN dbo.RequestTicket rt ON rt.DynamicFormValueId = dfv.DynamicFormValueId
	LEFT JOIN dbo.BusinessResult br ON br.Id = rt.TicketBusinessResultId
	WHERE df.Code = @ContestCode AND exi.Deleted = 0),
	AdjustEndorse AS( SELECT  pa.Amount, ifl.Name PaymentType, c.Name AgencyName, c.Code AgencyCode, temp.Name FieldName, 
	EndoreseTicketResultCode = CASE WHEN rt.Status = 1 THEN N'Mới tạo' + ISNULL('-' + br.Name, '') WHEN rt.Status = 2 THEN N'Chuyển xử lý' + ISNULL('-' + br.Name, '')
	WHEN rt.Status = 3 THEN N'Đang xử lý' + ISNULL('-' + br.Name, '') WHEN rt.Status = 4 THEN N'Hoàn thành' + ISNULL('-' + br.Name, '') ELSE br.Name End,
	EndorseServiceType = CASE WHEN pa.ServiceTypeId = 'AF9F94C9-BC33-475B-A45D-D470E0438C35' THEN 'Appeal'
	WHEN pa.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Endorsement'
	WHEN pa.ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' THEN 'Payment'
	ELSE NULL END, rt.Code EndoreseTicketCode , FORMAT (rt.CreatedDate, 'dd/MM/yyyy hh:mm') EndoreseTicketCreatedDate, pa.Id AdjustmentID, pa.Remark, FORMAT (pa.CreatedDate, 'dd/MM/yyyy hh:mm') ItemCreatedDate
	FROM dbo.PaymentAdjustment pa
	JOIN dbo.ServiceType st ON st.Id = pa.ContestId
	JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
	JOIN dbo.InfoList ifl ON ifl.Id = pa.PaymentType
	JOIN dbo.Customer c ON c.Id = pa.AgencyId
	JOIN (
		SELECT dfv.Id DynamicFieldValueId, dfd.Name, ei.Id ExpenseId FROM dbo.ExpenseItem ei
		JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
		JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
	) temp ON temp.ExpenseId = pa.ExpenseItemId
	LEFT JOIN dbo.DynamicFieldValue dfv ON dfv.Id = pa.EndorsementId
	LEFT JOIN dbo.RequestTicket rt ON rt.DynamicFormValueId = dfv.DynamicFormValueId
	LEFT JOIN dbo.BusinessResult br ON br.Id = rt.TicketBusinessResultId
	WHERE df.Code = @ContestCode AND pa.Deleted = 0 AND pa.Status = 1
	), AdjustPayment AS(SELECT  pa.Amount, ifl.Name PaymentType, c.Name AgencyName, c.Code AgencyCode, temp.Name FieldName,
	PaymentTicketResultCode = CASE WHEN rt.Status = 1 THEN N'Mới tạo' + ISNULL('-' + br.Name, '') WHEN rt.Status = 2 THEN N'Chuyển xử lý' + ISNULL('-' + br.Name, '')
	WHEN rt.Status = 3 THEN N'Đang xử lý' + ISNULL('-' + br.Name, '') WHEN rt.Status = 4 THEN N'Hoàn thành' + ISNULL('-' + br.Name, '') ELSE br.Name End,
	EndorseServiceType = CASE WHEN pa.ServiceTypeId = 'AF9F94C9-BC33-475B-A45D-D470E0438C35' THEN 'Appeal'
	WHEN pa.ServiceTypeId = '9C468B2E-3996-44E3-B98D-D40B8D029038' THEN 'Endorsement'
	WHEN pa.ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' THEN 'Payment'
	ELSE NULL END, rt.Code PaymentTicketCode , FORMAT (rt.CreatedDate, 'dd/MM/yyyy hh:mm') PaymentTicketCreatedDate, pa.Id AdjustmentID, pa.Remark, FORMAT (pa.CreatedDate, 'dd/MM/yyyy hh:mm') ItemCreatedDate
	FROM dbo.PaymentAdjustment pa
	JOIN dbo.ServiceType st ON st.Id = pa.ContestId
	JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
	JOIN dbo.InfoList ifl ON ifl.Id = pa.PaymentType
	JOIN dbo.Customer c ON c.Id = pa.AgencyId
	JOIN (
		SELECT dfv.Id DynamicFieldValueId, dfd.Name, ei.Id ExpenseId FROM dbo.ExpenseItem ei
		JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
		JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
	) temp ON temp.ExpenseId = pa.ExpenseItemId
	LEFT JOIN dbo.DynamicFieldValue dfv ON dfv.Id = pa.PaymentId
	LEFT JOIN dbo.RequestTicket rt ON rt.DynamicFormValueId = dfv.DynamicFormValueId
	LEFT JOIN dbo.BusinessResult br ON br.Id = rt.TicketBusinessResultId
	WHERE df.Code = @ContestCode AND pa.Deleted = 0 AND pa.Status = 1
	)
	SELECT @ContestName ContestName, @ContestCode ContestCode, ExpenseEndorse.AgencyCode, ExpenseEndorse.AgencyName, ExpenseEndorse.FieldName, ExpenseEndorse.Amount, ExpenseEndorse.PaymentType, 'ExpenseItem' [Type],
	ISNULL(ExpenseEndorse.ItemCreatedDate,ExpensePayment.ItemCreatedDate) ItemCreatedDate,ExpenseEndorse.EndorseServiceType, ExpenseEndorse.EndoreseTicketResultCode, ExpenseEndorse.EndoreseTicketCode, ExpenseEndorse.EndoreseTicketCreatedDate,
	ExpensePayment.PaymentTicketResultCode, ExpensePayment.PaymentTicketCode, ExpensePayment.PaymentTicketCreatedDate, NULL 'Remark' FROM ExpenseEndorse
	FULL OUTER JOIN ExpensePayment ON ExpenseEndorse.ExpenseItemId = ExpensePayment.ExpenseItemId
	UNION ALL
	SELECT @ContestName ContestName, @ContestCode ContestCode, AdjustEndorse.AgencyCode, AdjustEndorse.AgencyName, AdjustEndorse.FieldName, AdjustEndorse.Amount, AdjustEndorse.PaymentType, 'Adjustment' [Type],
	ISNULL(AdjustEndorse.ItemCreatedDate,AdjustPayment.ItemCreatedDate) ItemCreatedDate,AdjustEndorse.EndorseServiceType, AdjustEndorse.EndoreseTicketResultCode, AdjustEndorse.EndoreseTicketCode, AdjustEndorse.EndoreseTicketCreatedDate,
	AdjustPayment.PaymentTicketResultCode, AdjustPayment.PaymentTicketCode, AdjustPayment.PaymentTicketCreatedDate, ISNULL(AdjustEndorse.Remark, AdjustPayment.Remark) FROM AdjustEndorse
	FULL OUTER JOIN AdjustPayment ON AdjustEndorse.AdjustmentID = AdjustPayment.AdjustmentID

END
GO