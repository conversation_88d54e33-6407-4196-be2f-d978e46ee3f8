
/****** Object:  StoredProcedure [fwd].[GetScanEndorsementImportSessionRawItemListResult]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[GetScanEndorsementImportSessionRawItemListResult]

	@EndorsementImportSessionId UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	0 ErrorCode, N'Tổng số dòng' [Description], COUNT(*) CountItem
	FROM	fwd.EndorsementImportRawItem item
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
	UNION ALL
	SELECT	tempErrorCode.ErrorCode, tempErrorCode.Description, ISNULL(tempCount.CountItem,0) CountItem
	FROM	(
				SELECT	1 ErrorCode, N'Không tìm thấy Agency tương ứng.' [Description]
				UNION
				SELECT	2 ErrorCode, N'Không tìm thấy phiếu tham dự.' [Description]
				UNION
				SELECT	3 ErrorCode, N'Không tìm được Field cần Endorsement.' [Description]
				UNION
				SELECT	4 ErrorCode, N'Expense Item đã được endorse rồi.' [Description]
				UNION
				SELECT	5 ErrorCode, N'Không tìm được Expense Item cần Endorsement.' [Description]
			) tempErrorCode
			LEFT JOIN
			(
				SELECT	COUNT(*) CountItem, item.ErrorCode
				FROM	fwd.EndorsementImportRawItem item
				WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
						AND item.ErrorCode IS NOT NULL
				GROUP BY item.ErrorCode
			) tempCount ON tempErrorCode.ErrorCode = tempCount.ErrorCode

END
GO