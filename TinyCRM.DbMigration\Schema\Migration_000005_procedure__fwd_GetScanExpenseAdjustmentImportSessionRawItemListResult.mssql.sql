
/****** Object:  StoredProcedure [fwd].[GetScanExpenseAdjustmentImportSessionRawItemListResult]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [fwd].[GetScanExpenseAdjustmentImportSessionRawItemListResult]
	@ExpenseAdjustmentImportSessionId UNIQUEIDENTIFIER
AS
BEGIN

	SELECT	0 ErrorCode, N'Tổng số dòng' [Description], COUNT(*) CountItem
	FROM	fwd.ExpenseAdjustmentImportRawItem item
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
	UNION ALL
	SELECT	tempErrorCode.ErrorCode, tempErrorCode.Description, ISNULL(tempCount.CountItem,0) CountItem
	FROM	(
				SELECT	1 ErrorCode, N'Agency không tồn tại trong hệ thống.' [Description]
				UNION
				SELECT	2 ErrorCode, N'Không tìm thấy Contest.' [Description]
				UNION
				SELECT	3 ErrorCode, N'Agency không tồn tại trong Contest.' [Description]
				UNION
				SELECT	4 ErrorCode, N'Adjustment không link tới Expense Item.' [Description]
				UNION
				SELECT	5 ErrorCode, N'Payment Type không tồn tại trong hệ thống.' [Description]
			) tempErrorCode
			LEFT JOIN
			(
				SELECT	COUNT(*) CountItem, item.ErrorCode
				FROM	fwd.ExpenseAdjustmentImportRawItem item
				WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
						AND item.ErrorCode IS NOT NULL
				GROUP BY item.ErrorCode
			) tempCount ON tempErrorCode.ErrorCode = tempCount.ErrorCode

END
GO