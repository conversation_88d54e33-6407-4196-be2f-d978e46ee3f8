
/****** Object:  StoredProcedure [fwd].[GetScanPaymentRequestImportResult]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




CREATE PROCEDURE [fwd].[GetScanPaymentRequestImportResult]

	@PaymentRequestImportSessionId UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	0 ErrorCode, N'Tổng số dòng' [Description], COUNT(*) CountItem
	FROM	fwd.PaymentRequestImportRawItem item
	WHERE	item.PaymentRequestImportSessionId = @PaymentRequestImportSessionId
	UNION ALL
	SELECT	tempErrorCode.ErrorCode, tempErrorCode.Description, ISNULL(tempCount.CountItem,0) CountItem
	FROM	(
				SELECT	1 ErrorCode, N'Tên và mã Agency không tương ứng với nhau.' [Description]
				UNION
				SELECT	2 ErrorCode, N'Không đúng phiếu Endorsement đã chọn.' [Description]
				UNION
				SELECT	3 ErrorCode, N'Không tìm được Field của Endorsement.' [Description]
				UNION
				SELECT	4 ErrorCode, N'Mục này không có trong phiếu Endorsement đã chọn.' [Description]
				UNION
				SELECT	5 ErrorCode, N'Mục này đã được làm payment.' [Description]
				UNION
				SELECT	6 ErrorCode, N'Không xác định được Endorsement hoặc endorsement chưa được duyệt.' [Description] 
			) tempErrorCode
			LEFT JOIN
			(
				SELECT	COUNT(*) CountItem, item.ErrorCode
				FROM	fwd.PaymentRequestImportRawItem item
				WHERE	item.PaymentRequestImportSessionId = @PaymentRequestImportSessionId
						AND item.ErrorCode IS NOT NULL
				GROUP BY item.ErrorCode
			) tempCount ON tempErrorCode.ErrorCode = tempCount.ErrorCode

END
GO