
/****** Object:  StoredProcedure [fwd].[ImportContractRaw_Excute]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-----------------
CREATE PROCEDURE [fwd].[ImportContractRaw_Excute]
	@ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
	--Insert new Policy
	INSERT INTO fwd.ContractSyncRawItem(Id, TRANS_DATE, TRANS_TYPE, [POLICY], [STATUS], PLANCODE, SUBMIT_DATE, ISSUE_DATE, ACK_TRANSDATE, 
	FREELOOK_CANCELLATION_DATE, C_TAT02, BILL_FREQ, REGION, SALE_OFFICE, AGENTCODE, FULLNAME, No_Rider, Main_FYP, Main_FYC, Main_IP, 
	Main_APE, Rider_FYP, Rider_FYC, Rider_IP, Rider_APE, Total_FYP, Total_FYC, Total_IP, Total_APE, FieldString1, FieldString2, <PERSON>String3,
	FieldBool1, FieldBool2, FieldBool3, FieldDatetime1, FieldDatetime2, FieldDatetime3, FieldInt1, FieldInt2, FieldInt3,
	FieldDouble1, FieldDouble2, FieldDouble3)
	SELECT NEWID(), TRANS_DATE, TRANS_TYPE, [POLICY], [STATUS], PLANCODE, SUBMIT_DATE, ISSUE_DATE, ACK_TRANSDATE, 
	FREELOOK_CANCELLATION_DATE, C_TAT02, BILL_FREQ, REGION, SALE_OFFICE, AGENTCODE, FULLNAME, No_Rider, Main_FYP, Main_FYC, Main_IP, 
	Main_APE, Rider_FYP, Rider_FYC, Rider_IP, Rider_APE, Total_FYP, Total_FYC, Total_IP, Total_APE, FieldString1, FieldString2, FieldString3,
	FieldBool1, FieldBool2, FieldBool3, FieldDatetime1, FieldDatetime2, FieldDatetime3, FieldInt1, FieldInt2, FieldInt3,
	FieldDouble1, FieldDouble2, FieldDouble3
	FROM fwd.ImportContractRaw WHERE PolicyDuplicate = 0 AND ImportSessionId = @ImportSessionId AND MessageError IS NULL
	-- Update trùng policy
	UPDATE csr
	SET csr.TRANS_DATE = ISNULL(ic.TRANS_DATE,csr.TRANS_DATE) , csr.TRANS_TYPE = ISNULL(ic.TRANS_TYPE,csr.TRANS_TYPE), csr.[POLICY] = ISNULL(ic.[POLICY],csr.[POLICY]),
	csr.[STATUS] = ISNULL(ic.[STATUS],csr.[STATUS]) , csr.PLANCODE = ISNULL(ic.PLANCODE,csr.PLANCODE), csr.SUBMIT_DATE = ISNULL(ic.SUBMIT_DATE,csr.SUBMIT_DATE),
	csr.ISSUE_DATE = ISNULL(ic.ISSUE_DATE,csr.ISSUE_DATE) , csr.ACK_TRANSDATE = ISNULL(ic.ACK_TRANSDATE,csr.ACK_TRANSDATE), csr.REGION = ISNULL(ic.REGION,csr.REGION),
	csr.C_TAT02 = ISNULL(ic.C_TAT02,csr.C_TAT02) , csr.BILL_FREQ = ISNULL(ic.BILL_FREQ,csr.BILL_FREQ), csr.FREELOOK_CANCELLATION_DATE = ISNULL(ic.FREELOOK_CANCELLATION_DATE,csr.FREELOOK_CANCELLATION_DATE),
	csr.SALE_OFFICE = ISNULL(ic.SALE_OFFICE,csr.SALE_OFFICE) , csr.AGENTCODE = ISNULL(ic.AGENTCODE,csr.AGENTCODE), csr.FULLNAME = ISNULL(ic.FULLNAME,csr.FULLNAME),
    csr.No_Rider = ISNULL(ic.No_Rider,csr.No_Rider), csr.Main_FYP = ISNULL(ic.Main_FYP,csr.Main_FYP), csr.Main_FYC = ISNULL(ic.Main_FYC,csr.Main_FYC), 
	csr.Main_IP = ISNULL(ic.Main_IP,csr.Main_IP), csr.Main_APE = ISNULL(ic.Main_APE,csr.Main_APE), csr.Rider_FYP = ISNULL(ic.Rider_FYP,csr.Rider_FYP),
	csr.Rider_FYC = ISNULL(ic.Rider_FYC,csr.Rider_FYC), csr.Rider_IP = ISNULL(ic.Rider_IP,csr.Rider_IP), csr.Rider_APE = ISNULL(ic.Rider_APE,csr.Rider_APE),
	csr.Total_FYP = ISNULL(ic.Total_FYP,csr.Total_FYP), csr.Total_FYC = ISNULL(ic.Total_FYC,csr.Total_FYC), csr.Total_IP = ISNULL(ic.Total_IP,csr.Total_IP), csr.Total_APE = ISNULL(ic.Total_APE,csr.Total_APE),
	csr.FieldString1 = ISNULL(ic.FieldString1,csr.FieldString1), csr.FieldString2 = ISNULL(ic.FieldString2,csr.FieldString2), csr.FieldString3 = ISNULL(ic.FieldString3,csr.FieldString3),
	csr.FieldBool1 = ISNULL(ic.FieldBool1,csr.FieldBool1), csr.FieldBool2 = ISNULL(ic.FieldBool2,csr.FieldBool2), csr.FieldBool3 = ISNULL(ic.FieldBool3,csr.FieldBool3),
	csr.FieldDatetime1 = ISNULL(ic.FieldDatetime1,csr.FieldDatetime1), csr.FieldDatetime2 = ISNULL(ic.FieldDatetime2,csr.FieldDatetime2), csr.FieldDatetime3 = ISNULL(ic.FieldDatetime3,csr.FieldDatetime3),
	csr.FieldInt1 = ISNULL(ic.FieldInt1,csr.FieldInt1), csr.FieldInt2 = ISNULL(ic.FieldInt2,csr.FieldInt2), csr.FieldInt3 = ISNULL(ic.FieldInt3,csr.FieldInt3),
	csr.FieldDouble1 = ISNULL(ic.FieldDouble1,csr.FieldDouble1), csr.FieldDouble2 = ISNULL(ic.FieldDouble2,csr.FieldDouble2), csr.FieldDouble3 = ISNULL(ic.FieldDouble3,csr.FieldDouble3)
	FROM fwd.ContractSyncRawItem csr
	JOIN fwd.ImportContractRaw  ic ON csr.[POLICY] = ic.[POLICY]
	WHERE ic.PolicyDuplicate = 1 AND ic.ImportSessionId = @ImportSessionId AND ic.MessageError IS NULL

END
GO