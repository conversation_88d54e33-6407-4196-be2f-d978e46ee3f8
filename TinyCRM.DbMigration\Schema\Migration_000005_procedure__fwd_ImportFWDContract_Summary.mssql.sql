
/****** Object:  StoredProcedure [fwd].[ImportFWDContract_Summary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [fwd].[ImportFWDContract_Summary]
	@ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	WITH cte AS
	(
		SELECT  [POLICY] FROM fwd.ImportContractRaw WHERE PolicyDuplicate IS NULL AND ImportSessionId = @ImportSessionId
		GROUP BY [POLICY]
	) 
	SELECT COUNT(*) TotalRowData,
	(SELECT SUM(IIF(cte.[POLICY] IS NOT NULL,1,0)) FROM cte) TotalDuplicateInExcel,
	SUM(IIF(ic.MessageError IS NOT NULL AND ic.PolicyDuplicate IS NOT NULL OR ic.POLICY IS NULL,1,0)) TotalPolicyErr,
	SUM(IIF(ic.MessageError IS NULL AND ic.PolicyDuplicate = 0 ,1,0)) TotalNewPolicy,
	SUM(IIF(ic.MessageError IS NULL AND ic.PolicyDuplicate = 1 ,1,0)) TotalUpgradePolicy
	FROM fwd.ImportContractRaw ic WHERE ic.ImportSessionId = @ImportSessionId
END
GO