
/****** Object:  StoredProcedure [fwd].[ProcessEndorsementImportSessionRawItemList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

---------------------------

CREATE PROCEDURE [fwd].[ProcessEndorsementImportSessionRawItemList]

	@EndorsementImportSessionId UNIQUEIDENTIFIER,
	@EndorsementId UNIQUEIDENTIFIER,
	@ServiceTypeId UNIQUEIDENTIFIER,
	@RequestTicketId UNIQUEIDENTIFIER
AS
BEGIN

	BEGIN TRANSACTION trans;
	BEGIN TRY

		INSERT INTO fwd.ExpenseAndAdjustmentHistory(Id, RequestTicketId, ExpenseItemId, FileContestId,Type)
		SELECT NEWID(), @RequestTicketId, ei.Id, @EndorsementImportSessionId, 1
		FROM	fwd.EndorsementImportRawItem item
				JOIN dbo.ExpenseItem ei ON ei.Id = item.ExpenseItemId
		WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
				AND item.ErrorCode IS NULL

		UPDATE	ei
		SET		ei.EndorsementId = @EndorsementId, ei.EndorsementContestId = @EndorsementImportSessionId, ei.ServiceTypeId = @ServiceTypeId
		FROM	fwd.EndorsementImportRawItem item
				JOIN dbo.ExpenseItem ei ON ei.Id = item.ExpenseItemId
		WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
				AND item.ErrorCode IS NULL

		UPDATE	ec
		SET		ec.ContestId = temp.GroupCauseOfExpenseId
		FROM	fwd.EndorsementContest ec
				JOIN
                (
					SELECT	TOP 1 ei.GroupCauseOfExpenseId, item.EndorsementImportSessionId
					FROM	fwd.EndorsementImportRawItem item
							JOIN dbo.ExpenseItem ei ON ei.Id = item.ExpenseItemId
					WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
							AND item.ErrorCode IS NULL
				) temp ON temp.EndorsementImportSessionId = ec.Id

		COMMIT TRANSACTION trans;
	
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION trans;
		THROW;
	END CATCH

END
GO