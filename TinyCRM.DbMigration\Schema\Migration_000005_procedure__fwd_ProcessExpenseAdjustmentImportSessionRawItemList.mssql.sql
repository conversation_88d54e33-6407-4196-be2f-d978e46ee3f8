
/****** Object:  StoredProcedure [fwd].[ProcessExpenseAdjustmentImportSessionRawItemList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[ProcessExpenseAdjustmentImportSessionRawItemList]
    @ExpenseAdjustmentImportSessionId UNIQUEIDENTIFIER, @ExpenseAdjustmentId UNIQUEIDENTIFIER, @ServiceTypeId UNIQUEIDENTIFIER, @CreatedBy UNIQUEIDENTIFIER, @RequestTicketId UNIQUEIDENTIFIER
AS
BEGIN
    BEGIN TRANSACTION trans;
    BEGIN TRY

        DECLARE @ContestId UNIQUEIDENTIFIER;

        INSERT INTO dbo.PaymentAdjustment (Id, ContestId, AgencyId, Amount, PaymentType, ExpenseItemId, ServiceTypeId, EndorsementId, Remark, PaymentId, Deleted, CreatedDate, CreatedBy, Status)
        SELECT NEWID(), item.ServiceTypeId, item.CustomerId, item.Amount, item.PaymentType, item.ExpenseItemId, @ServiceTypeId, @ExpenseAdjustmentId, item.Remark,
               CASE WHEN @ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' THEN @ExpenseAdjustmentId ELSE NULL END, 0, [dbo].[Pop_GetDate](), @CreatedBy, 1
        FROM fwd.ExpenseAdjustmentImportRawItem item
        WHERE item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
              AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL);

        UPDATE fwd.ExpenseAdjustmentImportSession SET Status = 3 WHERE Id = @ExpenseAdjustmentImportSessionId;


        UPDATE exaj
        SET exaj.ContestId = temp.ServiceTypeId
        FROM fwd.ExpenseAdjustmentImportSession exaj
        JOIN (   SELECT TOP 1 item.ServiceTypeId, item.ExpenseAdjustmentImportSessionId
                 FROM fwd.ExpenseAdjustmentImportRawItem item
                 WHERE item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId) temp ON temp.ExpenseAdjustmentImportSessionId = exaj.Id;


        SELECT @ContestId = ContestId FROM fwd.ExpenseAdjustmentImportSession WHERE Id = @ExpenseAdjustmentImportSessionId;
        INSERT INTO fwd.ExpenseAndAdjustmentHistory (Id, RequestTicketId, AdjustmentId, FileContestId, Type)
        SELECT NEWID(), @RequestTicketId, pa.Id, @ExpenseAdjustmentImportSessionId, CASE WHEN @ServiceTypeId = '30007790-09CD-4D88-9ABC-2EBA70B1D0EB' THEN 2 ELSE 1 END
        FROM dbo.PaymentAdjustment pa
        WHERE (pa.EndorsementId = @ExpenseAdjustmentId AND pa.ContestId = @ContestId)
              OR (pa.PaymentId = @ExpenseAdjustmentId AND pa.ContestId = @ContestId);

        COMMIT TRANSACTION trans;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION trans;
        THROW;
    END CATCH;

END;
GO