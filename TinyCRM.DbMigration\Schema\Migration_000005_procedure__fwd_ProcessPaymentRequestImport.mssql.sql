
/****** Object:  StoredProcedure [fwd].[ProcessPaymentRequestImport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-----------
CREATE PROCEDURE [fwd].[ProcessPaymentRequestImport]

	@ImportSessionId UNIQUEIDENTIFIER,
	@PaymentId UNIQUEIDENTIFIER,
	@RequestTicketId UNIQUEIDENTIFIER

AS
BEGIN


	UPDATE	ei
	SET		ei.PaymentId = @PaymentId
	FROM	fwd.PaymentRequestImportRawItem item
			JOIN dbo.ExpenseItem ei ON ei.Id = item.ExpenseItemId
	WHERE	item.PaymentRequestImportSessionId = @ImportSessionId
			AND item.ErrorCode IS NULL

	UPDATE	ei
	SET		ei.PaymentId = @PaymentId
	FROM	fwd.PaymentRequestImportRawItem item
			JOIN dbo.PaymentAdjustment ei ON ei.Id = item.ExpenseItemId
	WHERE	item.PaymentRequestImportSessionId = @ImportSessionId
			AND item.ErrorCode IS NULL

	INSERT INTO fwd.ExpenseAndAdjustmentHistory(Id, RequestTicketId, ExpenseItemId,Type)
	SELECT NEWID(), @RequestTicketId, ei.Id, 2
	FROM dbo.ExpenseItem ei 
	WHERE ei.PaymentId = @PaymentId

	INSERT INTO fwd.ExpenseAndAdjustmentHistory(Id, RequestTicketId, ExpenseItemId,Type)
	SELECT NEWID(), @RequestTicketId, pa.Id, 2
	FROM dbo.PaymentAdjustment pa 
	WHERE pa.PaymentId = @PaymentId
END
GO