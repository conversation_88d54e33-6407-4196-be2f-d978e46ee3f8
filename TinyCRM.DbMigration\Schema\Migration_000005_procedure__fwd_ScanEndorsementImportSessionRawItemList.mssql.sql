
/****** Object:  StoredProcedure [fwd].[ScanEndorsementImportSessionRawItemList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [fwd].[ScanEndorsementImportSessionRawItemList]

	@EndorsementImportSessionId UNIQUEIDENTIFIER

AS
BEGIN

	-- 1. Tìm Agency tương ứng
	--====================================================================================
	UPDATE	item
	SET		item.CustomerId = c.Id
	FROM	fwd.EndorsementImportRawItem item
			JOIN dbo.Customer c ON c.Code = item.AgencyCode
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId

	UPDATE	item
	SET		item.ErrorCode = 1, item.ErrorMessage = N'Không tìm thấy Agency tương ứng.'
	FROM	fwd.EndorsementImportRawItem item
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.CustomerId IS NULL

	-- 2. Tìm phiếu yêu cầu tương ứng
	--====================================================================================
	UPDATE	item
	SET		item.RequestTicketId = rt.Id, item.ServiceTypeId = rt.ServiceTypeId
	FROM	fwd.EndorsementImportRawItem item
			JOIN dbo.Customer c ON c.Code = item.AgencyCode
			JOIN dbo.DynamicForm df ON df.Code = item.ContestCode
			JOIN dbo.ServiceType st ON st.DynamicFormId = df.Id
			JOIN dbo.RequestTicket rt ON rt.CustomerId = c.Id AND rt.ServiceTypeId = st.Id
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL

	UPDATE	item
	SET		item.ErrorCode = 2, item.ErrorMessage = N'Không tìm thấy phiếu tham dự.'
	FROM	fwd.EndorsementImportRawItem item
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL
			AND item.RequestTicketId IS NULL

	-- 3. Tìm DynamicFieldValue tương ứng
	--====================================================================================
	UPDATE	item
	SET		item.DynamicFieldValueId = dfv.Id
	FROM	fwd.EndorsementImportRawItem item
			JOIN dbo.RequestTicket rt ON rt.Id = item.RequestTicketId
			JOIN dbo.DynamicFormValue dfrv ON dfrv.Id = rt.DynamicFormValueId
			JOIN dbo.DynamicForm df ON df.Id = dfrv.DynamicFormId
			JOIN dbo.DynamicFieldDefinition dfd ON dfd.DynamicFormId = df.Id AND dfd.Name = item.FieldName
			JOIN dbo.DynamicFieldValue dfv ON dfv.DynamicFieldId = dfd.Id AND dfv.DynamicFormValueId = dfrv.Id
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL

	UPDATE	item
	SET		item.ErrorCode = 3, item.ErrorMessage = N'Không tìm được Field cần Endorsement.'
	FROM	fwd.EndorsementImportRawItem item
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL
			AND item.DynamicFieldValueId IS NULL

	-- 4. Check các ExpenseItem đã được endorse
	--====================================================================================
	UPDATE	item
	SET		item.ErrorCode = 4, item.ErrorMessage = N'Expense Item đã được endorse rồi.'
	FROM	fwd.EndorsementImportRawItem item
			JOIN dbo.ExpenseItem ei ON ei.CauseOfExpenseId = item.DynamicFieldValueId AND ei.GroupCauseOfExpenseId = item.ServiceTypeId
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL
			AND ei.EndorsementId IS NOT NULL

	-- 4. Tìm ExpenseItem tương ứng
	--====================================================================================
	UPDATE	item
	SET		item.ExpenseItemId = ei.Id
	FROM	fwd.EndorsementImportRawItem item
			JOIN dbo.ExpenseItem ei ON ei.CauseOfExpenseId = item.DynamicFieldValueId AND ei.GroupCauseOfExpenseId = item.ServiceTypeId
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL

	UPDATE	item
	SET		item.ErrorCode = 5, item.ErrorMessage = N'Không tìm được Expense Item cần Endorsement.'
	FROM	fwd.EndorsementImportRawItem item
	WHERE	item.EndorsementImportSessionId = @EndorsementImportSessionId
			AND item.ErrorCode IS NULL
			AND item.ExpenseItemId IS NULL

END
GO