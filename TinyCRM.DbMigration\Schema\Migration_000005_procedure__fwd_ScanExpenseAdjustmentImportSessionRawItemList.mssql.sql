
/****** Object:  StoredProcedure [fwd].[ScanExpenseAdjustmentImportSessionRawItemList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

----------------------------------------------
CREATE PROCEDURE [fwd].[ScanExpenseAdjustmentImportSessionRawItemList]
	@ExpenseAdjustmentImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    -- 1. Err Agency ko có trong hệ thống
	--====================================================================================
	UPDATE	item
	SET		item.CustomerId = c.Id
	FROM	fwd.ExpenseAdjustmentImportRawItem item
			JOIN dbo.Customer c ON c.Code = item.AgencyCode
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId

	UPDATE	item
	SET		item.ErrorCode = 1, item.ErrorMessage = N'Agency không tồn tại trong hệ thống'
	FROM	fwd.ExpenseAdjustmentImportRawItem item
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND item.CustomerId IS NULL

	-- 2. Tìm Contest tương ứng theo code
	----====================================================================================
	UPDATE	item
	SET		item.ServiceTypeId = st.Id
	FROM	fwd.ExpenseAdjustmentImportRawItem item
			JOIN dbo.DynamicForm df ON df.Code = item.ContestCode
			JOIN dbo.ServiceType st ON st.DynamicFormId = df.Id
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)

	UPDATE	item
	SET		item.ErrorCode = 3, item.ErrorMessage = N'Không tìm thấy Contest'
	FROM	fwd.ExpenseAdjustmentImportRawItem item
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)
			AND item.ServiceTypeId IS NULL

	-- 3. Tìm Agency tương ứng trong contest
	--====================================================================================
	UPDATE	item
	SET		item.CustomerInContest = 1
	FROM	fwd.ExpenseAdjustmentImportRawItem item
			JOIN dbo.RequestTicket rt ON item.CustomerId = rt.CustomerId
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId 
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL) AND Deleted = 0 AND rt.ServiceTypeId = item.ServiceTypeId

	UPDATE	item
	SET		item.ErrorCode = 2, item.ErrorMessage = N'Agency không tồn tại trong Contest'
	FROM	fwd.ExpenseAdjustmentImportRawItem item
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)
			AND item.CustomerInContest IS NULL

	-- 4. Tìm ExpenseItem tương ứng, warning
	----====================================================================================
	UPDATE	item
	SET		item.ExpenseItemId = ei.Id
	FROM dbo.ExpenseItem ei
	JOIN (
		SELECT dfv.Id DynamicFieldValueId, dfd.Name FROM dbo.ExpenseItem ei
		JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
		JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
	) temp ON temp.DynamicFieldValueId = ei.CauseOfExpenseId
	JOIN fwd.ExpenseAdjustmentImportRawItem item ON item.CustomerId = ei.PayeeId AND item.ServiceTypeId = ei.GroupCauseOfExpenseId AND item.FieldName = temp.Name
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)

	UPDATE	item
	SET		item.ErrorCode = 4, item.ErrorMessage = N'Không tìm thấy Expense Item cần Appeal Adjustment'
	FROM	fwd.ExpenseAdjustmentImportRawItem item
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)
			AND item.ExpenseItemId IS NULL
    -- 5. Tìm Payment tương ứng
	----====================================================================================
    UPDATE	item
	SET		item.PaymentType = IIF(item.PaymentTypeName IS NOT NULL OR item.PaymentTypeName <> '', ifl.Id, item.Id)
	FROM	fwd.ExpenseAdjustmentImportRawItem item
			LEFT JOIN dbo.InfoList ifl ON LOWER(ifl.Name) = LOWER(item.PaymentTypeName) AND ifl.Type = 'fwd_paymenttype'
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)

	UPDATE	item
	SET		item.ErrorCode = 5, item.ErrorMessage = N'Payment Type không tồn tại trong hệ thống'
	FROM	fwd.ExpenseAdjustmentImportRawItem item
	WHERE	item.ExpenseAdjustmentImportSessionId = @ExpenseAdjustmentImportSessionId
			AND (item.ErrorCode = 4 OR item.ErrorCode IS NULL)
			AND item.PaymentType IS NULL

END
GO