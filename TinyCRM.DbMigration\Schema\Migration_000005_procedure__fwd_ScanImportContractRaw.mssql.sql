
/****** Object:  StoredProcedure [fwd].[ScanImportContractRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

---------------------------------------------------------
CREATE PROCEDURE [fwd].[ScanImportContractRaw]
	@ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
	WITH cte AS 
	(
		SELECT import.[POLICY] policy_temp, cts.[POLICY] policy_import, import.ImportSessionId, import.PolicyDuplicate  FROM	fwd.ImportContractRaw import
	    LEFT JOIN fwd.ContractSyncRawItem cts ON cts.[POLICY] = import.[POLICY]
		WHERE import.[POLICY] IN (
		SELECT [POLICY]
		FROM fwd.ImportContractRaw
		WHERE ImportSessionId = @ImportSessionId
		GROUP BY [POLICY]
		HAVING COUNT([POLICY]) = 1
		)
	)
	UPDATE	cte
	SET		cte.PolicyDuplicate = IIF(cte.policy_import IS NOT NULL, 1, 0)
	WHERE   cte.ImportSessionId = @ImportSessionId

END
GO