
/****** Object:  StoredProcedure [fwd].[ScanPaymentRequestImport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [fwd].[ScanPaymentRequestImport]
    @ImportSessionId UNIQUEIDENTIFIER, @PaymentType UNIQUEIDENTIFIER, @EndorsementCode VARCHAR(20)
AS
BEGIN
	DECLARE @ApprovedBusinessResult UNIQUEIDENTIFIER = '11111111-1111-1111-1111-111111111111'
    SELECT dfv.Id
    INTO #endorseIdList
    FROM dbo.RequestTicket rt
    JOIN dbo.DynamicFieldValue dfv ON rt.DynamicFormValueId = dfv.DynamicFormValueId
    WHERE rt.Code = @EndorsementCode  AND rt.Status = 4 AND rt.TicketBusinessResultId = @ApprovedBusinessResult
    IF (SELECT COUNT(*)FROM #endorseIdList) > 0
    BEGIN
        SELECT ei.Id,
               ei.CauseOfExpenseId,
               ei.GroupCauseOfExpenseId,
               ei.Amount,
               ei.PayeeId,
               ei.PaymentType,
               ei.EndorsementId,
               ei.ServiceTypeId,
               c.Name AgencyName,
               c.Code AgencyCode,
               df.Name ContestName,
               df.Code ContestCode,
               dfd.Name FieldName
        INTO #endorseItems
        FROM #endorseIdList e
        JOIN dbo.ExpenseItem ei ON e.Id = ei.EndorsementId
        JOIN dbo.Customer c ON c.Id = ei.PayeeId
        JOIN dbo.ServiceType st ON st.Id = ei.GroupCauseOfExpenseId
        JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
        JOIN dbo.DynamicFieldValue dfv ON ei.CauseOfExpenseId = dfv.Id
        JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
        WHERE ei.PaymentType = @PaymentType;

        INSERT INTO #endorseItems (Id, CauseOfExpenseId, GroupCauseOfExpenseId, Amount, PayeeId, PaymentType, EndorsementId, ServiceTypeId, AgencyName, AgencyCode, ContestName, ContestCode, FieldName)
        SELECT adj.Id, ei.CauseOfExpenseId, adj.ContestId, adj.Amount, adj.AgencyId, adj.PaymentType, adj.EndorsementId, adj.ServiceTypeId, c.Name, c.Code, df.Name, df.Code, dfd.Name FieldName
        FROM #endorseIdList e
        JOIN dbo.PaymentAdjustment adj ON e.Id = adj.EndorsementId
        JOIN dbo.ExpenseItem ei ON adj.ExpenseItemId = ei.Id
        JOIN dbo.Customer c ON c.Id = ei.PayeeId
        JOIN dbo.ServiceType st ON st.Id = ei.GroupCauseOfExpenseId
        JOIN dbo.DynamicForm df ON df.Id = st.DynamicFormId
        JOIN dbo.DynamicFieldValue dfv ON ei.CauseOfExpenseId = dfv.Id
        JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
        WHERE adj.PaymentType = @PaymentType;

        -- 1. Tìm Agency tương ứng
        --====================================================================================
        UPDATE item
        SET item.CustomerId = i.PayeeId
        FROM fwd.PaymentRequestImportRawItem item
        JOIN #endorseItems i ON i.AgencyCode = item.AgencyCode
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId;

        UPDATE item
        SET item.CustomerId = i.PayeeId
        FROM fwd.PaymentRequestImportRawItem item
        JOIN #endorseItems i ON i.AgencyName = item.AgencyName
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.CustomerId IS NULL;

        UPDATE item
        SET item.ErrorCode = 1, item.ErrorMessage = N'Tên và mã Agency không tương ứng với nhau'
        FROM fwd.PaymentRequestImportRawItem item
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.CustomerId IS NULL;

        -- 2. Tìm phiếu yêu cầu tương ứng
        --====================================================================================
        UPDATE item
        SET item.ServiceTypeId = ei.GroupCauseOfExpenseId
        FROM fwd.PaymentRequestImportRawItem item
        JOIN #endorseItems ei ON ei.ContestCode = item.ContestCode
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ContestCode IS NOT NULL
              AND item.ErrorCode IS NULL
              AND item.ServiceTypeId IS NULL;
        UPDATE item
        SET item.ServiceTypeId = ei.GroupCauseOfExpenseId
        FROM fwd.PaymentRequestImportRawItem item
        JOIN #endorseItems ei ON ei.ContestName = item.ContestName
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ContestName IS NOT NULL
              AND item.ErrorCode IS NULL
              AND item.ServiceTypeId IS NULL;


        UPDATE item
        SET item.ErrorCode = 2, item.ErrorMessage = N'Không đúng phiếu Endorsement đã chọn.'
        FROM fwd.PaymentRequestImportRawItem item
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ErrorCode IS NULL
              AND item.ServiceTypeId IS NULL;

        -- 3. Tìm DynamicFieldValue tương ứng
        --====================================================================================
        UPDATE item
        SET item.DynamicFieldValueId = i.CauseOfExpenseId
        FROM fwd.PaymentRequestImportRawItem item
        JOIN #endorseItems i ON i.AgencyCode = item.AgencyCode
                                AND i.FieldName = item.FieldName
								AND i.GroupCauseOfExpenseId = item.ServiceTypeId
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ErrorCode IS NULL;

        UPDATE item
        SET item.ErrorCode = 3, item.ErrorMessage = N'Không tìm được Field của Endorsement.'
        FROM fwd.PaymentRequestImportRawItem item
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ErrorCode IS NULL
              AND item.DynamicFieldValueId IS NULL;
		




        -- 4. Check các ExpenseItem đã được endorse
        --====================================================================================

        UPDATE item
        SET item.EndorsementId = ei.EndorsementId, item.ExpenseItemId = ei.Id
        FROM fwd.PaymentRequestImportRawItem item
        JOIN #endorseItems ei ON ei.CauseOfExpenseId = item.DynamicFieldValueId
                                 AND ei.GroupCauseOfExpenseId = item.ServiceTypeId
                                 AND ei.PayeeId = item.CustomerId
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ErrorCode IS NULL
              AND item.EndorsementId IS  NULL;

        UPDATE item
        SET item.ErrorCode = 4, item.ErrorMessage = N'Mục này không có trong phiếu Endorsement đã chọn'
        FROM fwd.PaymentRequestImportRawItem item
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ErrorCode IS NULL
              AND (item.EndorsementId IS NULL OR item.ExpenseItemId IS NULL);

        -- 5. Check các ExpenseItem đã được Payment
        --====================================================================================
        UPDATE item
        SET item.ErrorCode = 5, item.ErrorMessage = N'Mục này đã được làm payment'
        FROM fwd.PaymentRequestImportRawItem item
        JOIN dbo.ExpenseItem ei ON ei.CauseOfExpenseId = item.DynamicFieldValueId
                                   AND ei.GroupCauseOfExpenseId = item.ServiceTypeId
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId
              AND item.ErrorCode IS NULL
              AND ei.PaymentId IS NOT NULL;

    END;
    ELSE
    BEGIN
        UPDATE item
        SET item.ErrorCode = 6, item.ErrorMessage = N'Không xác định được Endorsement'
        FROM fwd.PaymentRequestImportRawItem item
        WHERE item.PaymentRequestImportSessionId = @ImportSessionId;
    END;
END;
GO