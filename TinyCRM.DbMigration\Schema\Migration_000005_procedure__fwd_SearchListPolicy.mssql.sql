
/****** Object:  StoredProcedure [fwd].[SearchListPolicy]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [fwd].[SearchListPolicy]
	@Policy NVARCHAR(512) NULL,
	@AgencyCode NVARCHAR(512) NULL,
	@startRow INT,
	@endRow INT
AS
BEGIN
	WITH cte AS (
		SELECT pol.*, ROW_NUMBER() OVER (ORDER BY pol.POLICY) RowNumber FROM fwd.ContractSyncRawItem pol
		WHERE  (@Policy IS NULL OR (@Policy IS NOT NULL AND pol.POLICY COLLATE Latin1_general_CI_AI Like '%' + @Policy + '%' COLLATE Latin1_general_CI_AI))
			   AND (@AgencyCode IS NULL OR (@AgencyCode IS NOT NULL AND pol.AGENTCODE COLLATE Latin1_general_CI_AI Like '%' + @AgencyCode + '%' COLLATE Latin1_general_CI_AI))	
	)
	SELECT  cte.*, (SELECT COUNT(cte.RowNumber) FROM cte) TotalCount
	FROM  cte	
	WHERE  cte.RowNumber BETWEEN  @startRow AND  @endRow  
END
GO