
/****** Object:  StoredProcedure [fwd].[SyncAllPolicies]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[SyncAllPolicies]

	@ServiceTypeId UNIQUEIDENTIFIER,
	@ExecuteUserId UNIQUEIDENTIFIER,
	
	@PolicyFilterColumn NVARCHAR(512),
	@PolicyFilterColumnFromDate DATETIME,
	@PolicyFilterColumnToDate DATETIME,

	@PolicyFilterColumn2 NVARCHAR(512),
	@PolicyFilterColumn2FromDate DATETIME,
	@PolicyFilterColumn2ToDate DATETIME,

	@PolicyRoles dbo.StringString READONLY

AS
BEGIN

	DECLARE @DynamicFormId UNIQUEIDENTIFIER
	SELECT	@DynamicFormId = st.DynamicFormId
	FROM	dbo.ServiceType st
	WHERE	st.Id = @ServiceTypeId

	DECLARE @PolicyListDynamicFieldId UNIQUEIDENTIFIER
	DECLARE @DynamicDefinedTableSchemaId UNIQUEIDENTIFIER
	SELECT	@DynamicDefinedTableSchemaId = dfd.DynamicDefinedTableSchemaId, @PolicyListDynamicFieldId = dfd.Id
	FROM	dbo.DynamicFieldDefinition dfd
	WHERE	dfd.Deleted = 0 AND dfd.DynamicFormId = @DynamicFormId
			AND dfd.DynamicDefinedTableSchemaId IS NOT NULL

	IF @DynamicFormId IS NOT NULL AND @DynamicDefinedTableSchemaId IS NOT NULL BEGIN

		DECLARE @DynamicDefinedTableSchemaName NVARCHAR(MAX)
		SELECT	@DynamicDefinedTableSchemaName = Name
		FROM	dbo.DynamicDefinedTableSchema
		WHERE	Id = @DynamicDefinedTableSchemaId

		SELECT	defCol.*
		INTO	#TempColumns
		FROM	dbo.DynamicDefinedTableColumn defCol
		WHERE	defCol.DynamicDefinedTableSchemaId = @DynamicDefinedTableSchemaId
				AND defCol.Deleted = 0

		DECLARE @ExecutedString NVARCHAR(MAX) = N'
		SELECT	rt.Id RequestTicketId, dfv.Id DynamicFieldValueId, item.*, ROW_NUMBER() OVER (PARTITION BY rt.Id ORDER BY item.ISSUE_DATE DESC) RowNumber
		INTO	#TempPolicies
		FROM	dbo.RequestTicket rt WITH(NOLOCK)
				JOIN dbo.Customer c WITH(NOLOCK) ON c.Id = rt.CustomerId
				JOIN fwd.ContractSyncRawItem item WITH(NOLOCK) ON item.AGENTCODE = c.Code
				JOIN dbo.DynamicFieldValue dfv WITH(NOLOCK) ON dfv.DynamicFormValueId = rt.DynamicFormValueId AND dfv.DynamicFieldId = @PolicyListDynamicFieldId
		WHERE	rt.ServiceTypeId = @ServiceTypeId
				AND rt.Deleted = 0
		'

		IF EXISTS (SELECT * FROM @PolicyRoles) BEGIN

			DECLARE @GroupAgencyQueryString NVARCHAR(MAX) = ''
			DECLARE @SelectCteAgency NVARCHAR(MAX) = ''

			DECLARE @AgencyRole NVARCHAR(256)
			DECLARE @PolicyGroupMethod NVARCHAR(256)
			DECLARE @FirstPolicyRole BIT = 1

			DECLARE @TempPolicyRoles dbo.StringString
			INSERT @TempPolicyRoles (Value1, Value2)
			SELECT	Value1, Value2
			FROM	@PolicyRoles

			WHILE EXISTS (SELECT * FROM @TempPolicyRoles) BEGIN

				SELECT	TOP 1 @AgencyRole = Value1, @PolicyGroupMethod = Value2
				FROM	@TempPolicyRoles

				DECLARE @CteTableName NVARCHAR(256) = 'cte_' + @AgencyRole + '_' + @PolicyGroupMethod

				DECLARE @PolicyGroupMethodQuery NVARCHAR(MAX) = ''
				IF @PolicyGroupMethod = 'CaNhan' BEGIN
					SET @PolicyGroupMethodQuery = N'
					SELECT	c.Id, c.Code, c.Job, c.Nation, c.Code GroupCode, c.Id GroupId, ''CaNhan'' PolicyGroupMethod
					FROM	dbo.Customer c
					WHERE	c.IsDisabled=0 AND c.Job = ''' + @AgencyRole +  ''' '
				END
				ELSE IF @PolicyGroupMethod = 'ToanNhom' BEGIN
					SET @PolicyGroupMethodQuery = N'
					SELECT	c.Id, c.Code, c.Job, c.Nation, c.Code GroupCode, c.Id GroupId, ''ToanNhom'' PolicyGroupMethod
					FROM	dbo.Customer c
					WHERE	c.IsDisabled=0 AND c.Job = ''' + @AgencyRole +  '''
					UNION ALL
					SELECT	c.Id, c.Code, c.Job, c.Nation, cc.GroupCode, cc.GroupId, ''ToanNhom'' PolicyGroupMethod
					FROM	dbo.Customer c
							JOIN ' + @CteTableName + ' cc ON c.Nation = cc.Code
					WHERE	c.IsDisabled=0 '
				END
				ELSE IF @PolicyGroupMethod = 'ToanNhanh' BEGIN
					SET @PolicyGroupMethodQuery = N'
					SELECT	c.Id, c.Code, c.Job, c.Nation, c.Code GroupCode, c.Id GroupId, ''ToanNhanh'' PolicyGroupMethod
					FROM	dbo.Customer c
					WHERE	c.IsDisabled=0 AND c.Job = ''' + @AgencyRole +  '''
					UNION ALL
					SELECT	c.Id, c.Code, c.Job, c.Nation, cc.GroupCode, cc.GroupId, ''ToanNhanh'' PolicyGroupMethod
					FROM	dbo.Customer c
							JOIN ' + @CteTableName + ' cc ON c.Nation = cc.Code
					WHERE	c.IsDisabled=0 AND c.Job <> ''FWD'' '
				END
				ELSE IF @PolicyGroupMethod = 'NhomTrucTiep' BEGIN
					SET @PolicyGroupMethodQuery = N'
					SELECT	c.Id, c.Code, c.Job, c.Nation, c.Code GroupCode, c.Id GroupId, ''NhomTrucTiep'' PolicyGroupMethod
					FROM	dbo.Customer c
					WHERE	c.IsDisabled=0 AND c.Job = ''' + @AgencyRole +  '''
					UNION ALL
					SELECT	c.Id, c.Code, c.Job, c.Nation, cc.GroupCode, cc.GroupId, ''NhomTrucTiep'' PolicyGroupMethod
					FROM	dbo.Customer c
							JOIN ' + @CteTableName + ' cc ON c.Nation = cc.Code
					WHERE	c.IsDisabled=0 AND c.Job = ''FWP'' '
				END

				SET @GroupAgencyQueryString = @GroupAgencyQueryString + IIF(@GroupAgencyQueryString='', 'WITH ', ', ') + @CteTableName + ' AS
				(
					' + @PolicyGroupMethodQuery + '
				)
				'

				SET @SelectCteAgency = @SelectCteAgency + '
				SELECT DISTINCT Code, GroupId FROM ' + @CteTableName
				IF @FirstPolicyRole = 1 BEGIN
					SET @SelectCteAgency = '
					SELECT DISTINCT Code, GroupId
					INTO #TempAgencyGroup
					FROM ' + @CteTableName
				END

				DELETE @TempPolicyRoles WHERE Value1 = @AgencyRole AND Value2 = @PolicyGroupMethod
				SET @FirstPolicyRole = 0

				IF EXISTS (SELECT * FROM @TempPolicyRoles) BEGIN
					SET @SelectCteAgency = @SelectCteAgency + '
					UNION
					'
				END

			END

			SET @GroupAgencyQueryString = @GroupAgencyQueryString + @SelectCteAgency

			SET @ExecutedString = @GroupAgencyQueryString + N'

			SELECT	rt.Id RequestTicketId, dfv.Id DynamicFieldValueId, item.*, ROW_NUMBER() OVER (PARTITION BY rt.Id ORDER BY item.ISSUE_DATE DESC) RowNumber
			INTO	#TempPolicies
			FROM	dbo.RequestTicket rt WITH(NOLOCK)
					JOIN #TempAgencyGroup c WITH(NOLOCK) ON c.GroupId = rt.CustomerId
					JOIN fwd.ContractSyncRawItem item WITH(NOLOCK) ON item.AGENTCODE = c.Code
					JOIN dbo.DynamicFieldValue dfv WITH(NOLOCK) ON dfv.DynamicFormValueId = rt.DynamicFormValueId AND dfv.DynamicFieldId = @PolicyListDynamicFieldId
			WHERE	rt.ServiceTypeId = @ServiceTypeId
					AND rt.Deleted = 0
			'

		END

		DECLARE @WhereString NVARCHAR(MAX) = ''
		IF @PolicyFilterColumnFromDate IS NOT NULL BEGIN
			SET @WhereString = N' AND item.' + LTRIM(ISNULL(@PolicyFilterColumn,'ISSUE_DATE')) + ' >= @PolicyFilterColumnFromDate '
		END
		IF @PolicyFilterColumnToDate IS NOT NULL BEGIN
			SET @PolicyFilterColumnToDate = DATEADD(DAY, 1, @PolicyFilterColumnToDate)
			SET @WhereString = @WhereString + N' AND item.' + LTRIM(ISNULL(@PolicyFilterColumn,'ISSUE_DATE')) + ' < @PolicyFilterColumnToDate '
		END

		IF ISNULL(@PolicyFilterColumn2,'') <> '' BEGIN
			IF @PolicyFilterColumn2FromDate IS NOT NULL BEGIN
				SET @WhereString = N' AND item.' + LTRIM(@PolicyFilterColumn2) + ' >= @PolicyFilterColumn2FromDate '
			END
			IF @PolicyFilterColumn2ToDate IS NOT NULL BEGIN
				SET @PolicyFilterColumn2ToDate = DATEADD(DAY, 1, @PolicyFilterColumn2ToDate)
				SET @WhereString = @WhereString + N' AND item.' + LTRIM(@PolicyFilterColumn2) + ' < @PolicyFilterColumn2ToDate '
			END
		END

		SET @ExecutedString = @ExecutedString + @WhereString

		DECLARE @DynamicDefinedTableColumnId UNIQUEIDENTIFIER
		DECLARE @DynamicDefinedTableColumnName NVARCHAR(500)
		DECLARE @DynamicDefinedTableColumnType NVARCHAR(500)

		DECLARE @InsertDynamicDefinedTableCellValueString NVARCHAR(MAX) = N'
		INSERT dbo.DynamicDefinedTableCellValue	(Id, DynamicDefinedTableColumnId, DynamicFieldValueId, RowNumber, Value, CreatedBy, CreatedDate)
		'
		DECLARE @FirstRow BIT = 1
		WHILE (SELECT COUNT(*) FROM #TempColumns) > 0 BEGIN
		
			SELECT	TOP 1 @DynamicDefinedTableColumnId = Id, @DynamicDefinedTableColumnName = Name, @DynamicDefinedTableColumnType = DataType
			FROM	#TempColumns

			IF @FirstRow = 0 BEGIN
				SET @InsertDynamicDefinedTableCellValueString = @InsertDynamicDefinedTableCellValueString + N'
				UNION ALL
				'
			END

			IF EXISTS (SELECT * FROM sys.columns col WHERE col.Name = @DynamicDefinedTableColumnName AND col.OBJECT_ID = OBJECT_ID('fwd.ContractSyncRawItem')) BEGIN
				IF @DynamicDefinedTableColumnType LIKE N'%DateTime%' BEGIN
					SET @InsertDynamicDefinedTableCellValueString = @InsertDynamicDefinedTableCellValueString + N'
					SELECT	NEWID(), ''' + CAST(@DynamicDefinedTableColumnId AS NVARCHAR(256)) + ''', DynamicFieldValueId, RowNumber-1, IIF(' + @DynamicDefinedTableColumnName + ' IS NULL, '''', CONVERT(NVARCHAR(MAX), ' + @DynamicDefinedTableColumnName + ', 103)), @ExecuteUserId, GETDATE()
					FROM	#TempPolicies
					'
				END
				ELSE BEGIN
					SET @InsertDynamicDefinedTableCellValueString = @InsertDynamicDefinedTableCellValueString + N'
					SELECT	NEWID(), ''' + CAST(@DynamicDefinedTableColumnId AS NVARCHAR(256)) + ''', DynamicFieldValueId, RowNumber-1, IIF(' + @DynamicDefinedTableColumnName + ' IS NULL, '''', CAST(' + @DynamicDefinedTableColumnName + ' AS NVARCHAR(MAX))), @ExecuteUserId, GETDATE()
					FROM	#TempPolicies
					'
				END
			END
			ELSE BEGIN
				SET @InsertDynamicDefinedTableCellValueString = @InsertDynamicDefinedTableCellValueString + N'
					SELECT	NEWID(), ''' + CAST(@DynamicDefinedTableColumnId AS NVARCHAR(256)) + ''', DynamicFieldValueId, RowNumber-1, '''', @ExecuteUserId, GETDATE()
					FROM	#TempPolicies
					'
			END

			SET @FirstRow = 0
			DELETE FROM #TempColumns WHERE Id = @DynamicDefinedTableColumnId

		END

		SET @ExecutedString = @ExecutedString + @InsertDynamicDefinedTableCellValueString

		EXEC dbo.Proc_PrintLongText @LongString = @ExecutedString

		DECLARE @ParamDefs NVARCHAR(MAX) = N'
		@ServiceTypeId UNIQUEIDENTIFIER,
		@ExecuteUserId UNIQUEIDENTIFIER,
		@DynamicDefinedTableSchemaId UNIQUEIDENTIFIER,
		@PolicyFilterColumn NVARCHAR(512),
		@PolicyFilterColumnFromDate DATETIME,
		@PolicyFilterColumnToDate DATETIME,
		@PolicyFilterColumn2 NVARCHAR(512),
		@PolicyFilterColumn2FromDate DATETIME,
		@PolicyFilterColumn2ToDate DATETIME,
		@PolicyRoles dbo.StringString READONLY,
		@PolicyListDynamicFieldId UNIQUEIDENTIFIER'

		EXECUTE sp_executesql @ExecutedString, @ParamDefs,
												@ServiceTypeId = @ServiceTypeId,
												@ExecuteUserId = @ExecuteUserId,
												@DynamicDefinedTableSchemaId = @DynamicDefinedTableSchemaId,
												
												@PolicyFilterColumn = @PolicyFilterColumn,
												@PolicyFilterColumnFromDate = @PolicyFilterColumnFromDate,
												@PolicyFilterColumnToDate = @PolicyFilterColumnToDate,
												@PolicyFilterColumn2 = @PolicyFilterColumn2,
												@PolicyFilterColumn2FromDate = @PolicyFilterColumn2FromDate,
												@PolicyFilterColumn2ToDate = @PolicyFilterColumn2ToDate,

												@PolicyRoles = @PolicyRoles,
												@PolicyListDynamicFieldId = @PolicyListDynamicFieldId

	END

END
GO