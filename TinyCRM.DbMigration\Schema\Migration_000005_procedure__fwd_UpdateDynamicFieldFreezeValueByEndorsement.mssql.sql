
/****** Object:  StoredProcedure [fwd].[UpdateDynamicFieldFreezeValueByEndorsement]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [fwd].[UpdateDynamicFieldFreezeValueByEndorsement]

	@ContestId UNIQUEIDENTIFIER

AS
BEGIN

	UPDATE	dfd
	SET		dfd.FreezeValue = CASE WHEN temp.EndorsementCount > 0 THEN 1 ELSE 0 END
	FROM	dbo.DynamicFieldDefinition dfd
			JOIN
			(
				SELECT	dfd.Id DynamicFieldDefinitionId, SUM(CASE WHEN ei.EndorsementId IS NULL THEN 0 ELSE 1 END) EndorsementCount
				FROM	dbo.ExpenseItem ei
						JOIN dbo.DynamicFieldValue dfv ON dfv.Id = ei.CauseOfExpenseId
						JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
				WHERE	ei.GroupCauseOfExpenseId = @ContestId
				GROUP BY dfd.Id
			) temp ON temp.DynamicFieldDefinitionId = dfd.Id

END
GO