﻿
/****** Object:  StoredProcedure [import].[ImportContact_Campaign]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_Campaign]
       @ImportSessionId UNIQUEIDENTIFIER,
       @UserId UNIQUEIDENTIFIER,
       @CampaignId UNIQUEIDENTIFIER,
       @ErrorCode AS dbo.stringList READONLY
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
       DECLARE @CampaignType INT = (SELECT CampaignType FROM dbo.Campaign WHERE Id = @CampaignId)
       
       DECLARE @ReferenceResultType NVARCHAR(256)
       IF @CampaignType = 1
       BEGIN
         SET @ReferenceResultType = 'ContactCall'
       END
       ELSE
       BEGIN
         SET @ReferenceResultType = 'SurveyFeedback'
       END
       DECLARE @tableProspect TABLE (ProspectId UNIQUEIDENTIFIER, ProspectAssignmentId UNIQUEIDENTIFIER, CampaignId UNIQUEIDENTIFIER, ContactId UNIQUEIDENTIFIER)
       
       --ADD CONTACT TO CAMPAIGN
       INSERT INTO dbo.Prospect
              (
              [Id]
              ,[ContactId]
              ,[CampaignId]
              ,[CreatedBy]
              ,[Notes]
              ,[CreatedDate]
              ,[IsHot]
              ,[Status]
              ,[NextCallbackDate]
              ,[BackToCommonBasketDate]
              ,[ReprospectDate]
              ,[CurrentAssignmentId]
              ,[HotListGroupId]
              ,[LastCallId]
              ,[CallResultId]
              ,ReferenceObjectType
              ,ReferenceObjectId
              ,ReferenceResultType
              )
       OUTPUT Inserted.Id AS PropectId, 
			  Inserted.CurrentAssignmentId AS ProspectAssignmentId, 
			  Inserted.CampaignId AS CampaignId, 
			  Inserted.ContactId AS ContactId 
	   INTO @tableProspect

	   SELECT NEWID(),
              cig.ContactId,
              @CampaignId,
              @UserId,
              NULL,
              GETDATE(),
              0,
              1,
              NULL,
              NULL,
              NULL,
              NEWID(),
              NULL,
              NULL,
              NULL,
              'Contact',
              cig.ContactId ReferenceObjectId,
              @ReferenceResultType
       FROM   import.ContactRaw cig
			  JOIN dbo.Contact c on c.Id = cig.ContactId
       WHERE  ImportSessionId = @ImportSessionId
              AND ErrorCode IN (SELECT Value FROM @ErrorCode)

	  INSERT dbo.ProspectAssignment
              ( Id,
                ProspectId,
                CampaignId,
                ContactId,
                Status,
                CreatedDate,
                CreatedBy,
                Ignore15DaysRule,
                CreatedReason,
                ReferenceResultType
              )
       SELECT  ProspectAssignmentId, ProspectId, CampaignId, ContactId, 1, GETDATE(), @UserId, 0, 1, @ReferenceResultType
       FROM   @tableProspect
 ******/
END
GO