﻿using AutoMapper;
using System;
using System.Collections.Generic;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class DynamicDefinedTableCellValueData
    {
        public Guid? Id { get; set; }

        public Guid DynamicDefinedTableColumnId { get; set; }
        public string DynamicDefinedTableColumnName { get; set; }

        public Guid DynamicFieldValueId { get; set; }

        public int RowNumber { get; set; }

        public string Value { get; set; }

        public DynamicDefinedTableFileCellValue SingleFileData { get; set; }

        public List<DynamicDefinedTableFileCellValue> MultiFileDataList { get; set; }
    }

    public class DynamicDefinedTableCellValueItem
    {
        public Guid Id { get; set; }

        public Guid DynamicDefinedTableSchemaId { get; set; }
        public Guid DynamicDefinedTableColumnId { get; set; }
        public string DynamicDefinedTableColumnName { get; set; }
        public string DynamicDefinedTableColumnDataType { get; set; }
        public int DynamicDefinedTableColumnOrder { get; set; }
        public DynamicDefinedTableColumnType DynamicDefinedTableColumnType { get; set; }
        public string DynamicDefinedTableColumnDefaultValue { get; set; }

        public Guid DynamicFieldValueId { get; set; }

        public int RowNumber { get; set; }

        public string Value { get; set; }
    }

    public class RowNumberGroupItem
    {
        public int RowNumber { get; set; }

        public Guid RowId { get; set; }
    }

    public class RenderDynamicTableModel
    {
        public List<DynamicDefinedTableCellValueData> ListCellValue { get; set; }
        public List<DynamicDefinedTableColumnData> ListColumn { get; set; }
        public double? TimeZoneOffset { get; set; }
    }
}