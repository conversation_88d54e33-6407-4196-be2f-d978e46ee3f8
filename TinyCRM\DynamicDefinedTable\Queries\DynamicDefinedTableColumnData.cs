﻿using AutoMapper;
using System;
using Webaby.Core.DynamicForm.Queries;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class DynamicDefinedTableColumnData
    {
        public Guid Id { get; set; }

        public Guid DynamicDefinedTableSchemaId { get; set; }

        public DynamicDefinedTableColumnType ColumnType { get; set; }

        public string Name { get; set; }

        public string DisplayName { get; set; }

        public string DataType { get; set; }

        public string UiHint { get; set; }

        public int ColumnOrder { get; set; }

        public string AdditionalFilter { get; set; }

        public string DefaultValue { get; set; }

        public bool IsRequired { get; set; }

        public string Validation { get; set; }

        public string ValidationMessage { get; set; }

        public string SelectOptions { get; set; }

        public string RequiredDependency { get; set; }

        public string Inject { get; set; }

        public string AdditionalMetadata { get; set; }

        public bool Display { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public Boolean Deleted { get; set; }

        public DateTime? DeletedDate { get; set; }

        public Guid? DeletedBy { get; set; }

        public string UIFriendlyFormula { get; set; }

        public string Color { get; set; }

        public string BackgroundColor { get; set; }

        public string FomularByFieldName { get; set; }

        public bool IsSystemDefined { get; set; }

        public bool IsCheckDuplicateData { get; set; }

        public bool IsCheckDupOnDynamicForm { get; set; }
    }

    public static class DynamicDefinedTableColumnDataExtensions
    {
        public static DynamicFieldValueInfo ToDynamicFieldValueInfo(this DynamicDefinedTableColumnData dynamicDefinedTableColumnData, IMapper mapper)
        {
            DynamicFieldValueInfo dynamicFieldValueInfo = new DynamicFieldValueInfo();

            mapper.Map(dynamicDefinedTableColumnData, dynamicFieldValueInfo);

            dynamicFieldValueInfo.ViewHint = dynamicDefinedTableColumnData.UiHint;
            dynamicFieldValueInfo.Display = true;

            return dynamicFieldValueInfo;
        }
    }
}