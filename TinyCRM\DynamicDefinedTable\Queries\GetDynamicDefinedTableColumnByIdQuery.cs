﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class GetDynamicDefinedTableColumnByIdQuery : QueryBase<DynamicDefinedTableColumnData>
    {
        public Guid? DynamicDefinedTableColumnId { get; set; }

        public bool IncludedDeleted { get; set; }

        public string ColumnName { get; set; }

        public Guid? DynamicDefinedTableSchemaId { get; set; }
    }

    internal class GetDynamicDefinedTableColumnByIdQueryHandler : QueryHandlerBase<GetDynamicDefinedTableColumnByIdQuery, DynamicDefinedTableColumnData>
    {
        public GetDynamicDefinedTableColumnByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<DynamicDefinedTableColumnData>> ExecuteAsync(GetDynamicDefinedTableColumnByIdQuery query)
        {
            var dynamicDefinedTableColumnQuery = EntitySet.Get<DynamicDefinedTableColumnEntity>(query.IncludedDeleted).Where(col => col.Id == query.DynamicDefinedTableColumnId);
            if (query.DynamicDefinedTableSchemaId.HasValue && query.ColumnName.IsNotNullOrEmpty())
            {
                var listColumnName = EntitySet.Get<DynamicDefinedTableColumnEntity>().Where(x => x.DynamicDefinedTableSchemaId == query.DynamicDefinedTableSchemaId.Value && x.Name == query.ColumnName);
                dynamicDefinedTableColumnQuery = listColumnName;
            } else if(query.DynamicDefinedTableSchemaId.HasValue && query.ColumnName.IsNullOrEmpty())
            {
                var listColumnName = EntitySet.Get<DynamicDefinedTableColumnEntity>().Where(x => x.DynamicDefinedTableSchemaId == query.DynamicDefinedTableSchemaId.Value);
                dynamicDefinedTableColumnQuery = listColumnName;
            }

            var dynamicDefinedTableColumnEntityList = await dynamicDefinedTableColumnQuery.ToListAsync();
            var dynamicDefinedTableColumnList = Mapper.Map<List<DynamicDefinedTableColumnData>>(dynamicDefinedTableColumnEntityList);

            return QueryResult.Create(dynamicDefinedTableColumnList);
        }
    }
}