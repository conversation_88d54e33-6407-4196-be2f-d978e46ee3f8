﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class GetDynamicDefinedTableColumnListByTableSchemaQuery : QueryBase<DynamicDefinedTableColumnData>
    {
        public Guid DynamicDefinedTableSchemaId { get; set; }

        public string ColumnName { get; set; }

        public string FieldId { get; set; }
    }

    internal class GetDynamicDefinedTableColumnListByTableSchemaQueryHandler : QueryHandlerBase<GetDynamicDefinedTableColumnListByTableSchemaQuery, DynamicDefinedTableColumnData>
    {
        public GetDynamicDefinedTableColumnListByTableSchemaQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<DynamicDefinedTableColumnData>> ExecuteAsync(GetDynamicDefinedTableColumnListByTableSchemaQuery query)
        {
            var dynamicDefinedTableColumnQuery = await EntitySet.Get<DynamicDefinedTableColumnEntity>().Where(c => c.DynamicDefinedTableSchemaId == query.DynamicDefinedTableSchemaId).ToListAsync();
            if (query.ColumnName.IsNotNullOrEmpty())
            {
                dynamicDefinedTableColumnQuery = dynamicDefinedTableColumnQuery.Where(col => col.Name == query.ColumnName).ToList();
            }
            if (query.FieldId.IsNotNullOrEmpty())
            {
                var referenceDynamicFieldId = Guid.Parse(query.FieldId);
                var dynamicTableColumnOnDynamicForm = EntitySet.Get<DynamicDefinedTableColumnOnDynamicFormEntity>().Where(x => x.ReferenceDynamicFieldId == referenceDynamicFieldId).ToList();
                foreach(var column in dynamicDefinedTableColumnQuery)
                {
                    //Default display = true;
                    column.Display = true;
                    var mappingCol = dynamicTableColumnOnDynamicForm.FirstOrDefault(x => x.ReferenceColumnId == column.Id);
                    if(mappingCol != null)
                    {                        
                        column.Color = mappingCol.Color;
                        column.BackgroundColor = mappingCol.BackgroundColor;
                        column.Display = mappingCol.Display;

                        column.UiHint = mappingCol.UiHint;
                        column.ColumnType = mappingCol.ColumnType;
                        column.AdditionalMetadata = mappingCol.AdditionalMetadata;
                    }
                }
            }

            dynamicDefinedTableColumnQuery = dynamicDefinedTableColumnQuery.OrderBy(c => c.ColumnOrder).ToList();
            var dynamicDefinedTableColumnList = Mapper.Map<List<DynamicDefinedTableColumnData>>(dynamicDefinedTableColumnQuery);

            return QueryResult.Create(dynamicDefinedTableColumnList);
        }
    }
}