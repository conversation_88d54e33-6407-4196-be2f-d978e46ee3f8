﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery : QueryBase<DynamicDefinedTableLinkedTicketColumnData>
    {
        public Guid DynamicDefinedTableLinkedColumnId { get; set; }

        public Guid? OnDynamicFieldId { get; set; }
    }

    internal class GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQueryHandler : QueryHandlerBase<GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery, DynamicDefinedTableLinkedTicketColumnData>
    {
        public GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<DynamicDefinedTableLinkedTicketColumnData>> ExecuteAsync(GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery query)
        {
            var dynamicDefinedTableLinkedTicketColumnQuery = EntitySet.Get<DynamicDefinedTableLinkedTicketColumnEntity>().Where(c => c.DynamicDefinedTableLinkedColumnId == query.DynamicDefinedTableLinkedColumnId);
            if (query.OnDynamicFieldId.IsNotNullOrEmpty())
            {
                dynamicDefinedTableLinkedTicketColumnQuery = dynamicDefinedTableLinkedTicketColumnQuery.Where(c => c.OnDynamicFieldId == query.OnDynamicFieldId);
            }
            else
            {
                dynamicDefinedTableLinkedTicketColumnQuery = dynamicDefinedTableLinkedTicketColumnQuery.Where(c => c.OnDynamicFieldId.HasValue == false);
            }
            
            var dynamicDefinedTableLinkedTicketColumnEntityList = await dynamicDefinedTableLinkedTicketColumnQuery.OrderBy(c => c.DisplayOrder).ToListAsync();
            var dynamicDefinedTableLinkedTicketColumnList = Mapper.Map<List<DynamicDefinedTableLinkedTicketColumnData>>(dynamicDefinedTableLinkedTicketColumnEntityList);

            return QueryResult.Create(dynamicDefinedTableLinkedTicketColumnList);
        }
    }
}