﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class GetDynamicDefinedTableSchemaQuery : QueryBase<DynamicDefinedTableSchemaData>
    {
        public Guid? DynamicDefinedTableSchemaId { get; set; }
    }

    internal class GetDynamicDefinedTableSchemaQueryHandler : QueryHandlerBase<GetDynamicDefinedTableSchemaQuery, DynamicDefinedTableSchemaData>
    {
        public GetDynamicDefinedTableSchemaQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<DynamicDefinedTableSchemaData>> ExecuteAsync(GetDynamicDefinedTableSchemaQuery query)
        {
            var dynamicDefinedTableSchemaQuery = EntitySet.Get<DynamicDefinedTableSchemaEntity>();
            if (query.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
            {
                dynamicDefinedTableSchemaQuery = dynamicDefinedTableSchemaQuery.Where(tbl => tbl.Id == query.DynamicDefinedTableSchemaId);
            }

            var dynamicDefinedTableSchemaEntityList = await dynamicDefinedTableSchemaQuery.ToListAsync();
            var dynamicDefinedTableSchemaList = Mapper.Map<List<DynamicDefinedTableSchemaData>>(dynamicDefinedTableSchemaEntityList);

            return QueryResult.Create(dynamicDefinedTableSchemaList);
        }
    }
}