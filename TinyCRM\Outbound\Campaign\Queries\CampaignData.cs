﻿using AutoMapper;
using System;
using System.Collections.Generic;
using Webaby;
using TinyCRM.Enums;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Webaby.Core.File.Queries;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class CampaignData
    {
        public Guid Id { get; set; }

        public string CampaignName { get; set; }

        public string Description { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public CampaignStatus Status { get; set; }

        public string StatusChangeReason { get; set; }

        public CampaignType CampaignType { get; set; }

        public Guid? ResultCodeSuiteId { get; set; }

        public string IVR_DialRequestDef { get; set; }

        public DateTime? FinalResultDate { get; set; }

        public Guid? RefTicketApproveId { get; set; }

        public Guid? LastTaskUpdateId { get; set; }

        public IVRRequestConfig IVRDialRequestConfig
        {
            get
            {
                if (IVR_DialRequestDef.IsNotNullOrEmpty())
                {
                    try
                    {
                        var dialRequestConfig = JsonConvert.DeserializeObject<IVRRequestConfig>(IVR_DialRequestDef);
                        if (dialRequestConfig != null)
                        {
                            return dialRequestConfig;
                        }
                    }
                    catch (Exception) { }
                }
                return null;
            }
        }

        public string IVR_GetAvailableSlotRequestDef { get; set; }
        public IVRRequestConfig IVRGetAvailableSlotRequestConfig
        {
            get
            {
                if (IVR_GetAvailableSlotRequestDef.IsNotNullOrEmpty())
                {
                    try
                    {
                        var dialRequestConfig = JsonConvert.DeserializeObject<IVRRequestConfig>(IVR_GetAvailableSlotRequestDef);
                        if (dialRequestConfig != null)
                        {
                            return dialRequestConfig;
                        }
                    }
                    catch (Exception) { }
                }
                return null;
            }
        }

        public string IVR_GetAvailableAgentRequestDef { get; set; }
        public IVRRequestConfig IVRGetAvailableAgentRequestConfig
        {
            get
            {
                if (IVR_GetAvailableAgentRequestDef.IsNotNullOrEmpty())
                {
                    try
                    {
                        var dialRequestConfig = JsonConvert.DeserializeObject<IVRRequestConfig>(IVR_GetAvailableAgentRequestDef);
                        if (dialRequestConfig != null)
                        {
                            return dialRequestConfig;
                        }
                    }
                    catch (Exception) { }
                }
                return null;
            }
        }

        public string IVR_CallFlowId { get; set; }

        public int? IVR_MaxPoolSize { get; set; }

        public int? FailProtectionIntervalSeconds { get; set; }

        public int? FailProtectionMaxFailures { get; set; }

        public string CustomerDataOrderDef { get; set; }
        public List<CustomerDataOrderItem> CustomerDataOrderItemList
        {
            get
            {
                if (CustomerDataOrderDef.IsNotNullOrEmpty())
                {
                    try
                    {
                        var customerDataOrderItemList = JsonConvert.DeserializeObject<List<CustomerDataOrderItem>>(CustomerDataOrderDef);
                        if (customerDataOrderItemList != null)
                        {
                            return customerDataOrderItemList.OrderBy(x => x.Priority).ToList();
                        }
                    }
                    catch (Exception) { }
                }
                return null;
            }
        }

        public DateTime CreatedDate { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public bool Deleted { get; set; }

        public DateTime? DeletedDate { get; set; }

        public Guid? DeletedBy { get; set; }

        public Guid? DynamicFormId { get; set; }

        public string EmailTitle { get; set; }

        public string EmailContent { get; set; }

        public bool AutoSendWelcomeEmail { get; set; }

        public bool AutoAddCustomerBySavedSearch { get; set; }

        public string CustomerJobs { get; set; }

        public bool AutoSendSalePortal { get; set; }

        public bool AutoSendIRisPortal { get; set; }

        public string CampaignParticipants { get; set; }

        public string To { get; set; }

        public string Cc { get; set; }

        public string Bcc { get; set; }

        public List<FileData> EmailAttachmentFileList { get; set; }

        public List<FileData> FileEditList { get; set; }

        public Guid? CampaignFileId { get; set; }

        public Guid? DigitalPushCodeId { get; set; }

        public Guid? DigitalMessageTemplateId { get; set; }

        public bool AllowCustomerDuplication { get; set; }

        public string AdditionalDataDuplicationColumns { get; set; }

        public bool IsEnabledSchedule { get; set; }
    }

    public class IVRRequestConfig
    {
        public string Url { get; set; }

        [UIHint("Enum-v5")]
        public IVRRequestConfigMethod Method { get; set; }

        public List<IVRRequestParameter> Parameters { get; set; }

        public List<IVRRequestFile> Files { get; set; }
    }

    public enum IVRRequestConfigMethod
    {
        GET = 0,
        POST = 1,
        PUT = 2,
        DELETE = 3,
        HEAD = 4,
        OPTIONS = 5,
        PATCH = 6,
        MERGE = 7
    }

    public enum IVRRequestParameterType
    {
        Cookie = 0,
        GetOrPost = 1,
        UrlSegment = 2,
        HttpHeader = 3,
        RequestBody = 4,
        QueryString = 5
    }

    public class IVRRequestParameter
    {
        [Required(ErrorMessage = "Vui lòng nhập Parameter Name.")]
        public string Name { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập Parameter Value.")]
        public string Value { get; set; }

        public IVRRequestParameterType Type { get; set; }

        public string ContentType { get; set; }
    }

    public class IVRRequestFile
    {
        public string Name { get; set; }

        public string fileName { get; set; }

        public byte[] Bytes { get; set; }

        public string ContentType { get; set; }
    }
}