﻿using AutoMapper;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using TinyCRM.Behavior;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.RequestTicket.Queries
{
    public class GetRequestTicketByCodeQuery : QueryBase<RequestTicketData>
    {
        public string Code { get; set; }
    }

    internal class GetRequestTicketByCodeQueryHandler : QueryHandlerBase<GetRequestTicketByCodeQuery, RequestTicketData>
    {
        public GetRequestTicketByCodeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<RequestTicketData>> ExecuteAsync(GetRequestTicketByCodeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@Code", query.Code)
            });

            cmd.CommandText = "dbo.GetRequestTicketByCode";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<RequestTicketData>(cmd);

            foreach(var ticket in mainQuery)
            {
                var behaviorClassifications = (from behavior in EntitySet.Get<BehaviorEntity>()
                                               join ticketBehavior in EntitySet.Get<RequestTicketBehaviorEntity>() on behavior.Id equals ticketBehavior.BehaviorId
                                               where ticketBehavior.RequestTicketId == ticket.Id
                                               select behavior.Id).ToList();

                ticket.BehaviorClassifications = behaviorClassifications;
            }

            return QueryResult.Create(mainQuery);
        }
    }
}