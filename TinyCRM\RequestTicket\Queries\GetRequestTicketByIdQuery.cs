﻿using AutoMapper;
using System.Data;
using TinyCRM.Behavior;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.RequestTicket.Queries
{
    public class GetRequestTicketByIdQuery : QueryBase<RequestTicketData>
    {
        public GetRequestTicketByIdQuery(Guid id)
        {
            Id = id;
            IncludeDeleted = false;
        }

        public Guid Id { get; private set; }

        public bool IncludeDeleted { get; set; }
    }

    internal class GetRequestTicketByIdQueryHandler : QueryHandlerBase<GetRequestTicketByIdQuery, RequestTicketData>
    {
        public GetRequestTicketByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<RequestTicketData>> ExecuteAsync(GetRequestTicketByIdQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@Id", query.Id)
            });

            cmd.CommandText = "dbo.GetRequestTicketById";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<RequestTicketData>(cmd);

            var behaviorClassifications = (from behavior in EntitySet.Get<BehaviorEntity>()
                                           join ticketBehavior in EntitySet.Get<RequestTicketBehaviorEntity>() on behavior.Id equals ticketBehavior.BehaviorId
                                           where ticketBehavior.RequestTicketId == query.Id
                                           select behavior.Id).ToList();

            foreach (var ticket in mainQuery)
            {
                ticket.BehaviorClassifications = behaviorClassifications;
            }

            return QueryResult.Create(mainQuery);
        }
    }
}
