﻿using System;
using System.Collections.Generic;
using TinyCRM.Enums;

namespace TinyCRM.RequestTicket.Queries
{
    public class RequestTicketData
    {
        public Guid Id { get; set; }

        public string Code { get; set; }

        public string IsoCode { get; set; }

        public Guid CustomerId { get; set; }

        public string CustomerName { get; set; }

        public string CustomerCode { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }

        public List<Guid> BehaviorClassifications { get; set; }

        public Difficulty DifficultyDegree { get; set; }

        public int SourceChannel { get; set; }

        public int CustomerType { get; set; }

        public Guid ServiceTypeId { get; set; }

        public Guid? Level1Id { get; set; }

        public string Level1Name { get; set; }

        public string Level1Code { get; set; }

        public Guid? Level2Id { get; set; }

        public string Level2Name { get; set; }

        public string Level2Code { get; set; }

        public Guid? Level3Id { get; set; }

        public string Level3Name { get; set; }

        public string Level3Code { get; set; }

        public Guid? Level4Id { get; set; }

        public string Level4Name { get; set; }

        public string Level4Code { get; set; }

        public int? SoonerProcessDueMinutes { get; set; }

        public DateTime? ProcessSoonDueDate { get; set; }

        public DateTime? ProcessDueDate { get; set; }

        public Guid? ProcessDueTimeId { get; set; }

        public DateTime? AcceptDueDate { get; set; }

        public Guid? AcceptDueTimeId { get; set; }

        public DateTime? OpenTicketDate { get; set; }

        public DateTime? FinishedTicketDate { get; set; }

        public DateTime? AcceptedTicketDate { get; set; }

        public DateTime? PlannedDate { get; set; }

        public Guid? OwnerId { get; set; }

        public string OwnerName { get; set; }

        public RequestTicketStatus Status { get; set; }

        public Guid? CustomerAlternativeAddressId { get; set; }

        public string Notes { get; set; }

        public string Treatment { get; set; }

        public bool DelegatedTicket { get; set; }

        public TicketDelegatedRelationship? DelegatedRelationship { get; set; }

        public string DelegatedOtherRelationship { get; set; }

        public string RpName { get; set; }

        public string RpEmail { get; set; }

        public string RpPhone { get; set; }

        public string RpAddress { get; set; }

        public Guid? OwnedByOrganizationId { get; set; }

        public string OwnedByOrganizationName { get; set; }

        public Guid? ProvinceId { get; set; }

        public string ProvinceName { get; set; }

        public string SourceClassification { get; set; }

        public Guid? DynamicFormValueId { get; set; }

        public string ContextToken { get; set; }

        public bool IsNoneCustomerTicket { get; set; }

        public Guid? PartId { get; set; }

        public string PartName { get; set; }

        public Guid? BudgetId { get; set; }

        public Guid? DepartmentId { get; set; }

        public Guid? TicketBusinessResultId { get; set; }

        public Guid? ProspectAssignmentId { get; set; }

        public Guid? ProspectId { get; set; }

        public Guid? CampaignId { get; set; }

        #region Sản phẩm khiếu nại

        public string RelatedProductName { get; set; }

        public string ReleatedProductCode { get; set; }

        public string RelatedProductCategory { get; set; }

        public int? RelatedAffectedQuantity { get; set; }

        public Boolean? RelatedAffectedProductUsed { get; set; }

        public DateTime? RelatedProductDueDate { get; set; }

        public string RelatedProductFacCode { get; set; }

        public string RelatedProductFacName { get; set; }

        public int? RelatedBoughtQuantity { get; set; }

        public int? RelatedLeftQuantity { get; set; }

        public string RelatedBoughtFromStore { get; set; }

        public string RelatedMethodOfStorageInStore { get; set; }

        public string RelatedMethodOfStorageAtHome { get; set; }

        public string RelatedMethodOfUsingProduct { get; set; }

        public string RelatedTargetUserOfProduct { get; set; }

        public DateTime? RelatedDeliverDate { get; set; }

        public string RelatedDeliverCode { get; set; }

        #endregion

        #region Chuẩn bị gặp khách hàng

        public string CBGKH_AssignedUser { get; set; }

        public DateTime? CBGKH_DueTime { get; set; }

        public string CBGKH_Description { get; set; }

        public TinyCRM.Enums.TaskStatus? CBGKH_Status { get; set; }

        public TaskCompleteReason? CBGKH_CompleteReason { get; set; }

        #endregion

        #region Kiểm tra sản phẩm khiếu nại

        public string KTCLSP_AssignedUser { get; set; }

        public DateTime? KTCLSP_DueTime { get; set; }

        public string KTCLSP_Description { get; set; }

        public TinyCRM.Enums.TaskStatus? KTCLSP_Status { get; set; }

        public TaskCompleteReason? KTCLSP_CompleteReason { get; set; }

        #endregion

        public DateTime CreatedDate { get; set; }

        public Guid CreatedBy { get; set; }

        public string CreatedByName { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public Boolean Deleted { get; set; }

        public DateTime? DeletedDate { get; set; }

        public Guid? DeletedBy { get; set; }

        public TinyCRM.Enums.TaskStatus TaskStatus { get; set; }

        public string TaskType { get; set; }

        public string TaskOwerName { get; set; }

        public int TotalCount { get; set; }

        public Guid? WorkflowId { get; set; }

        public string ObjectApproveType { get; set; }
        
        public Guid? ObjectApproveId { get; set; }

        public DateTime? PlannedDate_Begin { get; set; }
        public DateTime? PlannedDate_End { get; set; }
    }
}
