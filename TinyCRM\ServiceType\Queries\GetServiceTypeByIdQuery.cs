﻿using AutoMapper;
using System;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeByIdQuery : QueryBase<ServiceTypeData>
    {
        public GetServiceTypeByIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; private set; }

        public bool IncludeDeleted { get; set; }
    }

    internal class GetServiceTypeByIdQueryHandler : QueryHandlerBase<GetServiceTypeByIdQuery, ServiceTypeData>
    {
        public GetServiceTypeByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(GetServiceTypeByIdQuery query)
        {
            var serviceTypeEntity = await EntitySet.GetAsync<ServiceTypeEntity>(query.Id, query.IncludeDeleted);
            var serviceType = Mapper.Map<ServiceTypeData>(serviceTypeEntity);

            return new QueryResult<ServiceTypeData>(serviceType);
        }
    }
}