﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyAnswerCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Answer { get; set; }

        public Guid? IconId { get; set; }

        public Guid? SurveyNextQuestionId { get; set; }

        public Guid SurveyQuestionId { get; set; }

        public double? RatingPoint { get; set; }

        public int? DisplayOrder { get; set; }
    }

    internal class CreateEditSurveyAnswerCommandHandler : CommandHandlerBase<CreateEditSurveyAnswerCommand>
    {
        public CreateEditSurveyAnswerCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyAnswerCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyAnswerEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyAnswerEntity();
            }
            entity.Id = command.Id;
            entity.Answer = command.Answer;
            entity.IconId = command.IconId;
            entity.SurveyNextQuestionId = command.SurveyNextQuestionId;
            entity.SurveyQuestionId = command.SurveyQuestionId;
            entity.DisplayOrder = command.DisplayOrder;
            entity.RatingPoint = command.RatingPoint;
            await Repository.SaveAsync(entity);
        }
    }
}