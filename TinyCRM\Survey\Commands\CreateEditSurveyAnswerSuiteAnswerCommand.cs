﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyAnswerSuiteAnswerCommand : CommandBase
    {
        public Guid SurveyAnswerId { get; set; }
        public Guid SurveyAnswerSuiteId { get; set; }
    }

    internal class CreateEditSurveyAnswerSuiteAnswerCommandHandler : CommandHandlerBase<CreateEditSurveyAnswerSuiteAnswerCommand>
    {
        public CreateEditSurveyAnswerSuiteAnswerCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyAnswerSuiteAnswerCommand command)
        {
            var entity = EntitySet.Get<SurveyAnswerSuiteAnswerEntity>()
                .Where(sa => sa.SurveyAnswerId == command.SurveyAnswerId && sa.SurveyAnswerSuiteId == command.SurveyAnswerSuiteId)
                .SingleOrDefault();
            if (entity == null)
            {
                entity = new SurveyAnswerSuiteAnswerEntity();
                entity.Id = Guid.Empty;
            }
            entity.SurveyAnswerId = command.SurveyAnswerId;
            entity.SurveyAnswerSuiteId = command.SurveyAnswerSuiteId;

            await Repository.SaveAsync(entity);
        }
    }
}