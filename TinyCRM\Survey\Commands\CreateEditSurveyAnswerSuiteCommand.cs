﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyAnswerSuiteCommand : CommandBase
    {
        public Guid Id { get; set; }
        public string Description { get; set; }
    }

    internal class CreateEditSurveyAnswerSuiteCommandHandler : CommandHandlerBase<CreateEditSurveyAnswerSuiteCommand>
    {
        public CreateEditSurveyAnswerSuiteCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyAnswerSuiteCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyAnswerSuiteEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyAnswerSuiteEntity();
            }
            entity.Id = command.Id;
            entity.Description = command.Description;

            await Repository.SaveAsync(entity);
        }
    }
}