﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyCampaignCommand : CommandBase
    {
        public Guid Id { get; set; }

        public SurveyType SurveyType { get; set; }

        public SurveyTarget Target { get; set; }

        public Guid SurveyId { get; set; }

        public int ExpiredTime { get; set; }

        public string SmsContent { get; set; }

        public string InvitationEmailTitle { get; set; }

        public string InvitationEmailTemplate { get; set; }

        public Guid CampaignId { get; set; }
    }

    internal class CreateEditSurveyCampaignCommandHandler : CommandHandlerBase<CreateEditSurveyCampaignCommand>
    {
        public CreateEditSurveyCampaignCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyCampaignCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyCampaignEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyCampaignEntity();
                entity.SurveyType = command.SurveyType;
                entity.SurveyId = command.SurveyId;
                entity.Target = command.Target;
            }
            entity.Id = command.Id;
            entity.SmsContent = command.SmsContent;
            entity.InvitationEmailTemplate = command.InvitationEmailTemplate;
            entity.InvitationEmailTitle = command.InvitationEmailTitle;
            entity.CampaignId = command.CampaignId;
            await Repository.SaveAsync(entity);
        }
    }
}
