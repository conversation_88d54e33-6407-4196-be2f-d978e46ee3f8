﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyCampaignExecutionCommand : CommandBase
    {
        public Guid Id { get; set; }
        public Guid SurveyCampaignId { get; set; }
        public Guid? ServiceTypeId { get; set; }
        public SurveyEvent? Event { get; set; }
    }

    internal class CreateEditSurveyCampaignExecutionCommandHandler : CommandHandlerBase<CreateEditSurveyCampaignExecutionCommand>
    {
        public CreateEditSurveyCampaignExecutionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyCampaignExecutionCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyCampaignExecutionEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyCampaignExecutionEntity();
            }
            entity.Id = command.Id;
            entity.SurveyCampaignId = command.SurveyCampaignId;
            entity.ServiceTypeId = command.ServiceTypeId;
            entity.Event = command.Event;
            await Repository.SaveAsync(entity);
        }
    }
}
