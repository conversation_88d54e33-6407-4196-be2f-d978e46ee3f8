﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyCampaignResponseExecutionCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid SurveyCampaignId { get; set; }

        public Guid SurveyQuestionId { get; set; }

        public Guid SurveyAnswerId { get; set; }

        public Guid ServiceTypeId { get; set; }

        public Guid? TicketOwnerId { get; set; }
    }

    internal class CreateEditSurveyCampaignResponseExecutionCommandHandler : CommandHandlerBase<CreateEditSurveyCampaignResponseExecutionCommand>
    {
        public CreateEditSurveyCampaignResponseExecutionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyCampaignResponseExecutionCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyCampaignResponseExecutionEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyCampaignResponseExecutionEntity();
            }
            entity.Id = command.Id;
            entity.SurveyCampaignId = command.SurveyCampaignId;
            entity.ServiceTypeId = command.ServiceTypeId;
            entity.SurveyQuestionId = command.SurveyQuestionId;
            entity.SurveyAnswerId = command.SurveyAnswerId;
            entity.TicketOwnerId = command.TicketOwnerId;

            await Repository.SaveAsync(entity);
        }
    }
}
