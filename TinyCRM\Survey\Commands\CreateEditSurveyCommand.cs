﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyCommand : CommandBase
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public int ExpiredTime { get; set; }
        public Guid? SurveyAnswerSuiteId { get; set; }
        public SurveyFlowType SurveyFlowType { get; set; }
    }

    internal class CreateEditSurveyCommandHandler : CommandHandlerBase<CreateEditSurveyCommand>
    {
        public CreateEditSurveyCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyEntity();
            }
            entity.Id = command.Id;
            entity.Title = command.Title;
            entity.ExpiredTime = command.ExpiredTime;
            entity.SurveyAnswerSuiteId = command.SurveyAnswerSuiteId;
            entity.SurveyFlowType = command.SurveyFlowType;

            await Repository.SaveAsync(entity);
        }
    }
}