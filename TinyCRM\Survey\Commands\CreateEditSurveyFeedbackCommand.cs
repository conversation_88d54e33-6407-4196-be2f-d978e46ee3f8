﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyFeedbackCommand : CommandBase
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public Guid SurveyId { get; set; }
        public Guid RelatedObjectId { get; set; }
        public Guid? SurveyeeId { get; set; }
        public Guid? SurveyeeOrganizationId { get; set; }
    }

    internal class CreateEditSurveyFeedbackCommandHandler : CommandHandlerBase<CreateEditSurveyFeedbackCommand>
    {
        public CreateEditSurveyFeedbackCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyFeedbackCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyFeedbackEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyFeedbackEntity();
            }
            entity.Id = command.Id;
            entity.Code = command.Code;
            entity.SurveyId = command.SurveyId;
            entity.RelatedObjectId = command.RelatedObjectId;
            entity.SurveyeeId = command.SurveyeeId;
            entity.SurveyeeOrganizationId = command.SurveyeeOrganizationId;

            await Repository.SaveAsync(entity);
        }
    }
}