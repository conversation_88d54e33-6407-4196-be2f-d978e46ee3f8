﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyQuestionCommand : CommandBase
    {
        public Guid Id { get; set; }
        public Guid SurveyId { get; set; }
        public string Question { get; set; }
        public AnswerType AnswerType { get; set; }
        public double? RootFlowOrder { get; set; }
        public Guid? SurveyAnswerSuiteId { get; set; }
        public Guid? SurveyQuestionSectionId { get; set; }
        public bool IsRequired { get; set; }
    }

    internal class CreateEditSurveyQuestionCommandHandler : CommandHandlerBase<CreateEditSurveyQuestionCommand>
    {
        public CreateEditSurveyQuestionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyQuestionCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyQuestionEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyQuestionEntity();
            }
            entity.Id = command.Id;
            entity.SurveyId = command.SurveyId;
            entity.Question = command.Question;
            entity.AnswerType = command.AnswerType;
            entity.RootFlowOrder = command.RootFlowOrder;
            entity.SurveyAnswerSuiteId = command.SurveyAnswerSuiteId;
            entity.SurveyQuestionSectionId = command.SurveyQuestionSectionId;
            entity.IsRequired = command.IsRequired;

            await Repository.SaveAsync(entity);

            // Nếu user nhập vào số lẻ, re-order lại question với giá trị nguyên
            if (command.RootFlowOrder.HasValue && command.RootFlowOrder.Value > 0 && command.RootFlowOrder.Value % 1 != 0)
            {
                var cmd = EntitySet.CreateDbCommand();
                cmd.CommandText = "ResetRootFlowOrders";
                cmd.CommandType = CommandType.StoredProcedure;
                DbParameterHelper.AddNullableGuid(cmd, "@SurveyId", command.SurveyId);
                await EntitySet.ExecuteNonQueryAsync(cmd);
            }

            // Nếu sử dụng AnswerSuite, phải xóa hết các câu trả lời đã tạo cho câu hỏi này
            if (command.SurveyAnswerSuiteId.HasValue && command.SurveyAnswerSuiteId.Value != Guid.Empty)
            {
                var answerList = EntitySet.Get<SurveyAnswerEntity>().Where(sa => sa.SurveyQuestionId == command.Id).ToList();
                await Repository.DeleteAsync(answerList);
            }
        }
    }
}