﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateEditSurveyQuestionSectionCommand : CommandBase
    {
        public Guid Id
        {
            get;
            set;
        }

        public Guid SurveyId
        {
            get;
            set;
        }

        public string SectionName
        {
            get; set;
        }

        public int DisplayOrder
        {
            get; set;
        }
    }

    internal class CreateEditSurveyQuestionSectionCommandHandler : CommandHandlerBase<CreateEditSurveyQuestionSectionCommand>
    {
        public CreateEditSurveyQuestionSectionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSurveyQuestionSectionCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyQuestionSectionEntity>(command.Id);
            if (entity == null)
            {
                entity = new SurveyQuestionSectionEntity();
            }
            entity.Id = command.Id;
            entity.SurveyId = command.SurveyId;
            entity.SectionName = command.SectionName;
            entity.DisplayOrder = command.DisplayOrder;

            await Repository.SaveAsync(entity);
        }
    }
}