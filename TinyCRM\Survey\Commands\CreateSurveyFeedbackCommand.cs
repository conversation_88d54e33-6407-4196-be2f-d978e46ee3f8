﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc;

namespace TinyCRM.Survey.Commands
{
    public class CreateSurveyFeedbackCommand: CommandBase
    {
        public UrlHelper Url { get; set; }
        public Action<string> RedirectFunc { get; set; }
        public Guid ProspectAssignmentId { get; set; }
        public Guid UserId { get; set; }
    }

    internal class CreateSurveyFeedbackCommandHandle : CommandHandlerBase<CreateSurveyFeedbackCommand>
    {
        public CreateSurveyFeedbackCommandHandle(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateSurveyFeedbackCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableGuid(cmd, "@ProspectAssignmentId", command.ProspectAssignmentId);
            DbParameterHelper.AddNullableGuid(cmd, "@UserId", command.UserId);
            cmd.CommandText = "[telesale].[CreateSurveyFeedback]";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<SurveyFeedbackEntity>(cmd);
            command.RedirectFunc.Invoke(command.Url.Action("DoSurvey", "Survey", new { sfc = mainQuery.ElementAt(0).Code }));
        }
    }
}
