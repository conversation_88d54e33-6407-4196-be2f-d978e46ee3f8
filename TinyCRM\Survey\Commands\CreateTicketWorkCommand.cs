﻿using System;
using System.Collections.Generic;
using System.Linq;
using TinyCRM.Campaign;
using TinyCRM.Enums;
using TinyCRM.RequestTicket;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class CreateTicketWorkCommand : CommandBase
    {
        public int AddCount { get; set; }
        public List<Guid> SelectedTicket { get; set; }
        public Guid CampaignId { get; set; }
        public Guid? OrganizationId { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public DateTime? FinishDateFrom { get; set; }
        public DateTime? FinishDateTo { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? ServiceTypeId { get; set; }
        public RequestTicketStatus? Status { get; set; }
    }

    internal class CreateTicketWorkCommandHandler : CommandHandlerBase<CreateTicketWorkCommand>
    {
        public CreateTicketWorkCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateTicketWorkCommand command)
        {
            var pyc = from p in EntitySet.Get<RequestTicketEntity>()
                      join w in EntitySet.Get<CampaignWorkEntity>().Where(x => x.CampaignId == command.CampaignId) on p.Id equals w.ReferenceObjectId into _w
                      from w in _w.DefaultIfEmpty()
                      where w == null
                      select p;
            if (command.SelectedTicket != null && command.SelectedTicket.Count > 0)
            {
                pyc = pyc.Where(x => command.SelectedTicket.Contains(x.Id));
            }
            pyc = pyc.Take(command.AddCount);
            var assignment = pyc.AsEnumerable().Select(x =>
            {
                return new CampaignWorkEntity
                {
                    Id = Guid.NewGuid(),
                    CampaignId = command.CampaignId,
                    ReferenceObjectId = x.Id,
                    ReferenceObjectType = "dbo.RequestTicket",                    
                    Deleted = false
                };
            });
            await Repository.SaveAsync(assignment);
        }
    }
}
