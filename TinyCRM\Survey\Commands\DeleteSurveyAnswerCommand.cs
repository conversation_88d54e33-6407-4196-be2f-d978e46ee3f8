﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class DeleteSurveyAnswerSuiteCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteSurveyAnswerSuiteCommandHandler : CommandHandlerBase<DeleteSurveyAnswerSuiteCommand>
    {
        public DeleteSurveyAnswerSuiteCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteSurveyAnswerSuiteCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyAnswerSuiteEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}