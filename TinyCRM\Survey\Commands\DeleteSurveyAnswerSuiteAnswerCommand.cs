﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class DeleteSurveyAnswerSuiteAnswerCommand : CommandBase
    {
        public Guid SurveyAnswerId { get; set; }
        public Guid SurveyAnswerSuiteId { get; set; }
    }

    internal class DeleteSurveyAnswerSuiteAnswerCommandHandler : CommandHandlerBase<DeleteSurveyAnswerSuiteAnswerCommand>
    {
        public DeleteSurveyAnswerSuiteAnswerCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteSurveyAnswerSuiteAnswerCommand command)
        {
            var entity = EntitySet.Get<SurveyAnswerSuiteAnswerEntity>()
                .Where(sa => sa.SurveyAnswerId == command.SurveyAnswerId && sa.SurveyAnswerSuiteId == command.SurveyAnswerSuiteId)
                .SingleOrDefault();
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}