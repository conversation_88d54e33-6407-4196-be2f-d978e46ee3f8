﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class DeleteSurveyAnswerCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteSurveyAnswerCommandHandler : CommandHandlerBase<DeleteSurveyAnswerCommand>
    {
        public DeleteSurveyAnswerCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteSurveyAnswerCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyAnswerEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}