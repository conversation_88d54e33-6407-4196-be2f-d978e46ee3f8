﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class DeleteSurveyCampaignCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteSurveyCampaignCommandHandler : CommandHandlerBase<DeleteSurveyCampaignCommand>
    {
        public DeleteSurveyCampaignCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteSurveyCampaignCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyCampaignEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}
