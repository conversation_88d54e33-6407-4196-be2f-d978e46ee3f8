﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class DeleteSurveyQuestionCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteSurveyQuestionCommandHandler : CommandHandlerBase<DeleteSurveyQuestionCommand>
    {
        public DeleteSurveyQuestionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteSurveyQuestionCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyQuestionEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}