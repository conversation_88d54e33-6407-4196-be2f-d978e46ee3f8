﻿using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class DeleteSurveyQuestionSectionCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteSurveyQuestionSectionCommandHandler : CommandHandlerBase<DeleteSurveyQuestionSectionCommand>
    {
        public DeleteSurveyQuestionSectionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteSurveyQuestionSectionCommand command)
        {
            var entity = await EntitySet.GetAsync<SurveyQuestionSectionEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }

            var questions = (from sq in EntitySet.Get<SurveyQuestionEntity>()
                             where sq.SurveyQuestionSectionId == command.Id
                             select sq).ToList();

            if (questions.Count > 0)
            {
                foreach (SurveyQuestionEntity questionItem in questions)
                {
                    questionItem.SurveyQuestionSectionId = null;
                }
                await Repository.SaveAsync(questions);
            }
        }
    }
}