﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Customer.Queries;
using TinyCRM.Outbound.Contact.Queries;
using TinyCRM.Outbound.ProspectAssignment;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class ProcessSurveyCampaignResponseExecutionCommand : CommandBase
    {
        public Guid SurveyFeedbackId { get; set; }
        public Dictionary<Guid, List<Guid>> SurveyFeedbackAnswers { get; set; }
    }

    internal class ProcessSurveyCampaignResponseExecutionCommandHandler : CommandHandlerBase<ProcessSurveyCampaignResponseExecutionCommand>
    {
        public IConfiguration _configuration;
        
        public string CreateTicketApiLink { get { return _configuration.GetValue<string>("createticketapilink"); } }
        
        public string CustomerUserName { get { return _configuration.GetValue<string>("customer.username"); } }
        
        public string CustomerPassword { get { return _configuration.GetValue<string>("customer.password"); } }
        
        public ProcessSurveyCampaignResponseExecutionCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus            
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(ProcessSurveyCampaignResponseExecutionCommand command)
        {
            var prospectAssignment = EntitySet.Get<ProspectAssignmentEntity>().Where(pa => pa.ReferenceResultId == command.SurveyFeedbackId).SingleOrDefault();
            if (prospectAssignment != null)
            {
                var surveyCampaign = EntitySet.Get<SurveyCampaignEntity>().Where(sc => sc.CampaignId == prospectAssignment.CampaignId).SingleOrDefault();
                if (surveyCampaign != null)
                {
                    var surveyCampaignResponseExecutions = (from re in EntitySet.Get<SurveyCampaignResponseExecutionEntity>()
                                                            where re.SurveyCampaignId == surveyCampaign.Id
                                                            select re).ToList();

                    foreach (var surveyFeedbackAnswer in command.SurveyFeedbackAnswers)
                    {
                        var responseExecutions = (from re in surveyCampaignResponseExecutions
                                                            where re.SurveyQuestionId == surveyFeedbackAnswer.Key
                                                            && surveyFeedbackAnswer.Value.Contains(re.SurveyAnswerId)
                                                            select re).ToList();

                        foreach (var surveyCampaignResponseExecution in responseExecutions)
                        {
                            var serviceType = await EntitySet.GetAsync<ServiceTypeEntity>(surveyCampaignResponseExecution.ServiceTypeId);
                            if (serviceType != null)
                            {
                                CreateTicketRequest createTicketRequest = new CreateTicketRequest();
                                createTicketRequest.CustomerId = prospectAssignment.CustomerId;
                                createTicketRequest.Level1Id = serviceType.Level1Id;
                                createTicketRequest.Level2Id = serviceType.Level2Id;
                                createTicketRequest.Level3Id = serviceType.Level3Id;
                                createTicketRequest.Level4Id = serviceType.Level4Id;
                                createTicketRequest.OwnerId = surveyCampaignResponseExecution.TicketOwnerId;

                                var customer = await QueryExecutor.ExecuteOneAsync(new GetCustomerByIdQuery { Id = createTicketRequest.CustomerId });
                                if (customer != null)
                                {
                                    Dictionary<string, string> headers = new Dictionary<string, string>();
                                    headers.Add("CIF", customer.Code);
                                    headers.Add("CustomerUsername", CustomerUserName);
                                    headers.Add("CustomerPassword", CustomerPassword);

                                    var result = RestHelper<CreateTicketResult>.ApiPostCall(CreateTicketApiLink, "POST", createTicketRequest, RestBodyOptions.Json, null, headers);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public class CreateTicketRequest
    {
        public Guid CustomerId { get; set; }
        public Guid? Level1Id { get; set; }
        public Guid? Level2Id { get; set; }
        public Guid? Level3Id { get; set; }
        public Guid? Level4Id { get; set; }
        public Guid? OwnerId { get; set; }
    }

    public class CreateTicketResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
        public string SuccessMessage { get; set; }
        public List<string> WarningMessage { get; set; }
    }
}