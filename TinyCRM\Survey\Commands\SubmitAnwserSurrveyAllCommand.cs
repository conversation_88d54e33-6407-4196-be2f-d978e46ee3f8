﻿using System;
using Webaby;
using System.Collections.Generic;
using System.Linq;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Survey.Commands
{
    public class SubmitSurveyAnwserAllCommand : CommandBase
    {
        public Guid SurveyFeedbackId { get; set; }
        public Dictionary<string, string> Text { get; set; }
        public Dictionary<string, string> Single { get; set; }
        public Dictionary<string, List<string>> Multi { get; set; }
    }

    internal class SubmitAnwserSurrveyAllCommandHandler : CommandHandlerBase<SubmitSurveyAnwserAllCommand>
    {
        public SubmitAnwserSurrveyAllCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(SubmitSurveyAnwserAllCommand command)
        {
            List<SurveyFeedbackAnswerEntity> feedback_anwser = new List<SurveyFeedbackAnswerEntity>();
            foreach (var t in command.Text)
            {
                Guid qId = Guid.Parse(t.Key);
                var fa = EntitySet.Get<SurveyFeedbackAnswerEntity>().Where(x => x.SurveyFeedbackId == command.SurveyFeedbackId && x.SurveyQuestionId == qId).FirstOrDefault();
                if (fa == null)
                {
                    fa = new SurveyFeedbackAnswerEntity
                    {                        
                        Id = Guid.NewGuid(),
                        SurveyAnswerId = null,
                        SurveyAnswerParentId = null,
                        SurveyAnswerText = t.Value,
                        SurveyFeedbackId = command.SurveyFeedbackId,
                        SurveyQuestionId = qId,
                    };
                }
                fa.SurveyAnswerText = t.Value;
                feedback_anwser.Add(fa);
            }
            foreach (var s in command.Single)
            {
                Guid qId = Guid.Parse(s.Key);
                Guid? aId = string.IsNullOrWhiteSpace(s.Value) ? new Guid?() : Guid.Parse(s.Value);
                var fa = EntitySet.Get<SurveyFeedbackAnswerEntity>().Where(x => x.SurveyFeedbackId == command.SurveyFeedbackId && x.SurveyQuestionId == qId).FirstOrDefault();
                if (fa == null)
                {
                    fa = new SurveyFeedbackAnswerEntity
                    {                        
                        Id = Guid.NewGuid(),
                        SurveyAnswerId = null,
                        SurveyAnswerParentId = null,
                        SurveyAnswerText = null,
                        SurveyFeedbackId = command.SurveyFeedbackId,
                        SurveyQuestionId = qId,
                    };
                }
                fa.SurveyAnswerId = aId;
                feedback_anwser.Add(fa);
            }
            foreach (var m in command.Multi)
            {
                Guid qId = Guid.Empty;
                if (Guid.TryParse(m.Key, out qId))
                {
                    var fa = EntitySet.Get<SurveyFeedbackAnswerEntity>().Where(x => x.SurveyFeedbackId == command.SurveyFeedbackId && x.SurveyQuestionId == qId);
                    await Repository.DeleteAsync(fa);
                    foreach (var i in m.Value)
                    {
                        Guid aId = Guid.Empty;
                        if (Guid.TryParse(i, out aId))
                        {
                            feedback_anwser.Add(new SurveyFeedbackAnswerEntity
                            {                                
                                Id = Guid.NewGuid(),
                                SurveyAnswerId = aId,
                                SurveyAnswerParentId = null,
                                SurveyAnswerText = null,
                                SurveyFeedbackId = command.SurveyFeedbackId,
                                SurveyQuestionId = qId
                            });
                        }
                    }
                }
            }
            if(feedback_anwser != null && feedback_anwser.Count > 0)
            {
                await Repository.SaveAsync(feedback_anwser);
            }
        }
    }
}
