﻿using System;
using Webaby;
using System.Collections.Generic;
using System.Linq;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class SubmitSurveyAnwserCommand : CommandBase
    {
        public AnswerType AnswerType { get; set; }

        public Guid surveyFeedbackId { get; set; }

        public Guid surveyQuestionId { get; set; }

        public List<Guid> surveyAnswerId { get; set; }

        public Guid? surveyAnswerParentId { get; set; }

        public string surveyAnswerText { get; set; }
    }

    internal class SubmitAnwserSurrveyCommandHandler : CommandHandlerBase<SubmitSurveyAnwserCommand>
    {
        public SubmitAnwserSurrveyCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(SubmitSurveyAnwserCommand command)
        {
            List<SurveyFeedbackAnswerEntity> feedback_anwser = EntitySet.Get<SurveyFeedbackAnswerEntity>().Where(x => x.SurveyFeedbackId == command.surveyFeedbackId && x.SurveyQuestionId == command.surveyQuestionId).ToList();
            feedback_anwser = command.surveyAnswerParentId.HasValue ? feedback_anwser.Where(x => x.SurveyAnswerParentId == command.surveyAnswerParentId).ToList() : feedback_anwser.Where(x => !x.SurveyAnswerParentId.HasValue).ToList();

            if (command.AnswerType == AnswerType.Text)
            {
                if (command.surveyAnswerId != null && command.surveyAnswerId.Count != 0 && feedback_anwser.Count > 1)
                {
                    throw new Exception("Lỗi");
                }
                if (!feedback_anwser.Any())
                {
                    feedback_anwser.Add(new SurveyFeedbackAnswerEntity
                    {                        
                        Id = Guid.NewGuid(),
                        SurveyAnswerId = null,
                        SurveyAnswerParentId = command.surveyAnswerParentId,
                        SurveyAnswerText = command.surveyAnswerText,
                        SurveyFeedbackId = command.surveyFeedbackId,
                        SurveyQuestionId = command.surveyQuestionId
                    });
                }
                else
                {
                    feedback_anwser.First().SurveyAnswerText = command.surveyAnswerText;
                }
            }
            else if (command.AnswerType == AnswerType.SingleSelect)
            {
                if (command.surveyAnswerId.Count != 1 && feedback_anwser.Count > 1)
                {
                    throw new Exception("Lỗi");
                }

                Guid? surveyAnswerId = null;
                if (command.surveyAnswerId != null && command.surveyAnswerId.Count > 0)
                {
                    surveyAnswerId = command.surveyAnswerId.First();
                }
                if (!feedback_anwser.Any())
                {
                    feedback_anwser.Add(new SurveyFeedbackAnswerEntity
                    {                        
                        Id = Guid.NewGuid(),
                        SurveyAnswerId = surveyAnswerId,
                        SurveyAnswerParentId = command.surveyAnswerParentId,
                        SurveyAnswerText = null,
                        SurveyFeedbackId = command.surveyFeedbackId,
                        SurveyQuestionId = command.surveyQuestionId
                    });
                }
                else
                {
                    var childList = EntitySet.Get<SurveyFeedbackAnswerEntity>().Where(x => x.SurveyFeedbackId == feedback_anwser.First().SurveyFeedbackId && x.SurveyAnswerParentId == feedback_anwser.First().SurveyAnswerId).ToList();
                    await Repository.DeleteAsync(childList);
                    feedback_anwser.First().SurveyAnswerId = surveyAnswerId;
                }
            }
            else if (command.AnswerType == AnswerType.MultiSelect)
            {
                await Repository.DeleteAsync(feedback_anwser);
                if (command.surveyAnswerId != null)
                {
                    List<SurveyFeedbackAnswerEntity> newList = new List<SurveyFeedbackAnswerEntity>();
                    foreach (var i in command.surveyAnswerId)
                    {
                        newList.Add(new SurveyFeedbackAnswerEntity
                        {                            
                            Id = Guid.NewGuid(),
                            SurveyAnswerId = i,
                            SurveyAnswerParentId = command.surveyAnswerParentId,
                            SurveyAnswerText = null,
                            SurveyFeedbackId = command.surveyFeedbackId,
                            SurveyQuestionId = command.surveyQuestionId
                        });
                    }
                    feedback_anwser = newList;
                }
                else
                {
                    feedback_anwser = new List<SurveyFeedbackAnswerEntity>();
                }
            }
            await Repository.SaveAsync(feedback_anwser);
        }
    }
}
