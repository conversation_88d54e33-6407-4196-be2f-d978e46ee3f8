﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Survey.Commands
{
    public class SubmitSurveyFeedbackCommand : CommandBase
    {
        public Guid SurveyFeedbackId { get; set; }
        public List<SubmitSurveyQuestionCommand> SubmitSurveyQuestionList { get; set; }
    }

    internal class SubmitSurveyFeedbackCommandHandler : CommandHandlerBase<SubmitSurveyFeedbackCommand>
    {
        public SubmitSurveyFeedbackCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(SubmitSurveyFeedbackCommand command)
        {
            List<SurveyFeedbackAnswerEntity> savedEntities = new List<SurveyFeedbackAnswerEntity>();

            foreach (var questionItem in command.SubmitSurveyQuestionList)
            {
                // Nếu là câu hỏi dạng nhập text
                if (questionItem.AnswerType == AnswerType.Text)
                {
                    SurveyFeedbackAnswerEntity answerEnity = new SurveyFeedbackAnswerEntity();
                    answerEnity.Id = Guid.NewGuid();
                    answerEnity.SurveyFeedbackId = command.SurveyFeedbackId;
                    answerEnity.SurveyQuestionId = questionItem.SurveyQuestionId;
                    answerEnity.SurveyAnswerText = questionItem.SurveyAnswerTextValue;
                    answerEnity.SurveyAnswerParentId = questionItem.SurveyAnswerParentId;

                    savedEntities.Add(answerEnity);
                }
                else
                {
                    // Nếu câu hỏi dạng chọn câu trả lời, lưu mỗi câu trả lòi thành 1 SurveyFeedbackAnswer
                    if (questionItem.SurveyAnswerIds != null && questionItem.SurveyAnswerIds.Count > 0)
                    {
                        foreach (var answerId in questionItem.SurveyAnswerIds)
                        {
                            SurveyFeedbackAnswerEntity answerEnity = new SurveyFeedbackAnswerEntity();
                            answerEnity.Id = Guid.NewGuid();
                            answerEnity.SurveyFeedbackId = command.SurveyFeedbackId;
                            answerEnity.SurveyQuestionId = questionItem.SurveyQuestionId;
                            answerEnity.SurveyAnswerId = answerId;
                            answerEnity.SurveyAnswerParentId = questionItem.SurveyAnswerParentId;

                            savedEntities.Add(answerEnity);
                        }
                    }
                }
            }

            foreach (SurveyFeedbackAnswerEntity answerItem in savedEntities)
            {
                if (answerItem.SurveyAnswerParentId.HasValue && answerItem.SurveyAnswerParentId.Value != Guid.Empty)
                {
                    var parentAnswerId = (from tt in savedEntities
                                          where tt.SurveyAnswerId == answerItem.SurveyAnswerParentId.Value
                                          select tt.Id).SingleOrDefault();
                    answerItem.SurveyAnswerParentId = parentAnswerId;
                }
            }

            await Repository.SaveAsync(savedEntities);
        }
    }

    public class SubmitSurveyQuestionCommand
    {
        public Guid SurveyQuestionId { get; set; }
        public AnswerType AnswerType { get; set; }
        public List<Guid> SurveyAnswerIds { get; set; }
        public string SurveyAnswerTextValue { get; set; }
        public Guid? SurveyAnswerParentId { get; set; }
    }
}
