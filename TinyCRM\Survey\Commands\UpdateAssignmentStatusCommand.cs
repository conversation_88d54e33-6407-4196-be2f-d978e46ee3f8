﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Campaign;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using TinyCRM.Outbound.Campaign;

namespace TinyCRM.Survey.Commands
{
    public class UpdateAssignmentStatusCommand : CommandBase
    {
        public Guid FeedbackId { get; set; }
        public AssignmentStatus OldStatus { get; set; }
        public AssignmentStatus NewStatus { get; set; }
    }

    internal class UpdateAssignmentStatusCommandHandler : CommandHandlerBase<UpdateAssignmentStatusCommand>
    {
        public UpdateAssignmentStatusCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(UpdateAssignmentStatusCommand command)
        {
            var assignment = (from w in EntitySet.Get<CampaignWorkEntity>()
                              join c in EntitySet.Get<CampaignEntity>() on w.CampaignId equals c.Id
                              join sc in EntitySet.Get<SurveyCampaignEntity>() on c.Id equals sc.CampaignId
                              join fb in EntitySet.Get<SurveyFeedbackEntity>() on w.ResultId equals fb.Id
                              join ca in EntitySet.Get<CampaignAssignmentEntity>().Where(x => x.Status == command.OldStatus) on w.CurrentAssignmentId equals ca.Id
                              where fb.Id == command.FeedbackId && sc.SurveyType == SurveyType.Automatic
                              select ca).OrderByDescending(x => x.CreatedDate).FirstOrDefault();
            if(assignment != null)
            {
                assignment.Status = command.NewStatus;
                await Repository.SaveAsync(assignment);
            }
        }
    }
}
