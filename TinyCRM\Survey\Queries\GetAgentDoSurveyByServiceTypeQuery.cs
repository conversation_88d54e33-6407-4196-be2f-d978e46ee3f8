﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetAgentDoSurveyByServiceTypeQuery : QueryBase<SurveyData>
    {
        public Guid ServiceTypeId { get; set; }
    }

    internal class GetAgentDoSurveyByServiceTypeQueryHandler : QueryHandlerBase<GetAgentDoSurveyByServiceTypeQuery, SurveyData>
    {
        public GetAgentDoSurveyByServiceTypeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyData>> ExecuteAsync(GetAgentDoSurveyByServiceTypeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableGuid(cmd, "@ServiceTypeId", query.ServiceTypeId);
            cmd.CommandText = "dbo.GetAgentDoSurveyByServiceType";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<SurveyData>(cmd);
            return new QueryResult<SurveyData>(mainQuery);
        }
    }
}