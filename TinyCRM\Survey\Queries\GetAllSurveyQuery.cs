﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetAllSurveyQuery : QueryBase<SurveyData>
    {
    }

    internal class GetAllSurveyQueryHandler : QueryHandlerBase<GetAllSurveyQuery, SurveyData>
    {
        public GetAllSurveyQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyData>> ExecuteAsync(GetAllSurveyQuery query)
        {
            var entities = await EntitySet.GetAsync<SurveyEntity>();
            return QueryResult.Create(entities, Mapper.Map<SurveyData>);
        }
    }
}
