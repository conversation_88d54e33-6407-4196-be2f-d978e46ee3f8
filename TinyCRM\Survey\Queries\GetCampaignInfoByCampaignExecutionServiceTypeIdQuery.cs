﻿using AutoMapper;
using System;
using System.Linq;
using TinyCRM.Outbound.Campaign;
using TinyCRM.Outbound.Campaign.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetCampaignInfoByCampaignExecutionServiceTypeIdQuery : QueryBase<CampaignSurveyCampaignInfo>
    {
        public Guid ServiceTypeId { get; set; }
    }

    internal class GetCampaignInfoByCampaignExecutionInfoQueryHandler : QueryHandlerBase<GetCampaignInfoByCampaignExecutionServiceTypeIdQuery, CampaignSurveyCampaignInfo>
    {
        public GetCampaignInfoByCampaignExecutionInfoQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
        : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<CampaignSurveyCampaignInfo>> ExecuteAsync(GetCampaignInfoByCampaignExecutionServiceTypeIdQuery query)
        {
            var result = (from execution in EntitySet.Get<SurveyCampaignExecutionEntity>()
                          join sc in EntitySet.Get<SurveyCampaignEntity>() on execution.SurveyCampaignId equals sc.Id
                          join c in EntitySet.Get<CampaignEntity>() on sc.CampaignId equals c.Id
                          where execution.ServiceTypeId == query.ServiceTypeId
                          select new CampaignSurveyCampaignInfo
                          {
                              CampaignData = Mapper.Map<CampaignData>(c),
                              SurveyCampaignData = Mapper.Map<SurveyCampaignData>(sc)
                          });
            return new QueryResult<CampaignSurveyCampaignInfo>(result);
        }
    }
}
