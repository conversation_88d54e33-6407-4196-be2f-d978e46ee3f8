﻿using AutoMapper;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetCampaignSurveyQuestionListQuery : QueryBase<SurveyQuestionData>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetCampaignSurveyQuestionListQueryHandler : QueryHandlerBase<GetCampaignSurveyQuestionListQuery, SurveyQuestionData>
    {
        public GetCampaignSurveyQuestionListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
        : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionData>> ExecuteAsync(GetCampaignSurveyQuestionListQuery query)
        {
            var mainQuery = (from sc in EntitySet.Get<SurveyCampaignEntity>()
                             join s in EntitySet.Get<SurveyEntity>() on sc.SurveyId equals s.Id
                             join sq in EntitySet.Get<SurveyQuestionEntity>() on s.Id equals sq.SurveyId
                             orderby sq.RootFlowOrder
                             where sc.CampaignId == query.CampaignId
                             select sq);

            return QueryResult.Create(mainQuery, query.Pagination, x => Mapper.Map<SurveyQuestionData>(x));
        }
    }
}