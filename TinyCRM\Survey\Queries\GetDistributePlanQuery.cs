﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Organization;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetDistributePlanQuery : QueryBase<GetDistributePlanQuery.Result>
    {
        //search
        public int? Status { get; set; }
        public Guid? OrganizationId { get; set; }
        //
        public Guid UserId { get; set; }
        public Guid CampaignId { get; set; }
        public Dictionary<string, string> Model { get; set; }
        public int Mode { get; set; }
        public bool Execute { get; set; }
        public class Result
        {
            public Guid UserId { get; set; }
            public int Quantity { get; set; }
        }
    }

    internal class GetDistributePlanQueryHandler : QueryHandlerBase<GetDistributePlanQuery, GetDistributePlanQuery.Result>
    {
        public GetDistributePlanQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
        : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<GetDistributePlanQuery.Result>> ExecuteAsync(GetDistributePlanQuery query)
        {
            var tableParam = new DataTable();
            tableParam.Columns.Add("Key", typeof(Guid));
            tableParam.Columns.Add("Value", typeof(int));

            query.Model.Each(x =>
            {
                if (x is KeyValuePair<string, string> kvp)
                {
                    var newRow = tableParam.NewRow();
                    newRow["Key"] = Guid.Parse(kvp.Key);
                    newRow["Value"] = int.Parse(kvp.Value);
                    tableParam.Rows.Add(newRow);
                }
            });
            var cmd = EntitySet.CreateDbCommand();
            var userPlan =  new SqlParameter("@UserPlan", tableParam);
            userPlan.SqlDbType = SqlDbType.Structured;
            userPlan.TypeName = "dbo.KeyValue";

            cmd.Parameters.Add(userPlan);

            cmd.Parameters.AddRange(new[]
            {                
                DbParameterHelper.AddNullableGuid(cmd, "@UserId", query.UserId),
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId),
                DbParameterHelper.AddNullableInt(cmd, "@DistributeMode", query.Mode),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@Planing", !query.Execute),
                DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId),
                DbParameterHelper.AddNullableInt(cmd ,"@Status", query.Status)
            });
            cmd.CommandText = "dbo.DistributeWork";
            cmd.CommandType = CommandType.StoredProcedure;

            var result = await EntitySet.ExecuteReadCommandAsync<GetDistributePlanQuery.Result>(cmd);
            return QueryResult.Create(result);
        }
    }
}
