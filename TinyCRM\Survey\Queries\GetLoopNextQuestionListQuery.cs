﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetLoopNextQuestionListQuery : QueryBase<LoopNextQuestionListItem>
    {
        public Guid SurveyQuestionId { get; set; }
        public Guid? SurveyNextQuestionId { get; set; }
    }

    internal class GetLoopNextQuestionListQueryHandler : QueryHandlerBase<GetLoopNextQuestionListQuery, LoopNextQuestionListItem>
    {
        public GetLoopNextQuestionListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<LoopNextQuestionListItem>> ExecuteAsync(GetLoopNextQuestionListQuery query)
        {
            //var cmd = EntitySet.CreateDbCommand();
            //cmd.CommandType = CommandType.Text;
            //cmd.CommandText = @"
            //WITH cte AS
            //(
            //\tSELECT\tsq.Id QuestionId, sa.Id AnswerId, sa.SurveyNextQuestionId
            //\tFROM\tdbo.SurveyQuestion sq
            //\t\t\tJOIN dbo.SurveyAnswer sa ON sa.SurveyQuestionId = sq.Id
            //\tWHERE\tsq.Deleted = 0 AND sa.Deleted = 0
            //\t\t\tAND sq.Id = @SurveyNextQuestionId
            //\tUNION ALL
            //\tSELECT\tsq.Id QuestionId, sa.Id AnswerId, sa.SurveyNextQuestionId
            //\tFROM\tcte temp
            //\t\t\tJOIN dbo.SurveyQuestion sq ON sq.Id = temp.SurveyNextQuestionId
            //\t\t\tJOIN dbo.SurveyAnswer sa ON sa.SurveyQuestionId = sq.Id
            //\tWHERE\tsq.Deleted = 0 AND sa.Deleted = 0
            //)
            //SELECT\t*
            //FROM\tcte
            //WHERE\tcte.QuestionId = @SurveyQuestionId
            //";
            //DbParameterHelper.AddNullableGuid(cmd, "@SurveyQuestionId", query.SurveyQuestionId);
            //DbParameterHelper.AddNullableGuid(cmd, "@SurveyNextQuestionId", query.SurveyNextQuestionId);
            //var mainQuery = await EntitySet.ExecuteReadCommandAsync<LoopNextQuestionListItem>(cmd);
            //return QueryResult.Create(mainQuery);

            // Use LINQ instead of CommandText
            // Build a list to simulate the recursive CTE
            var allQuestions = EntitySet.Get<SurveyQuestionEntity>().Where(q => !q.Deleted).ToList();
            var allAnswers = EntitySet.Get<SurveyAnswerEntity>().Where(a => !a.Deleted).ToList();

            var result = new List<LoopNextQuestionListItem>();

            void Traverse(Guid? nextQuestionId)
            {
                if (!nextQuestionId.HasValue) return;
                var question = allQuestions.FirstOrDefault(q => q.Id == nextQuestionId.Value);
                if (question == null) return;
                var answers = allAnswers.Where(a => a.SurveyQuestionId == question.Id).ToList();
                foreach (var answer in answers)
                {
                    result.Add(new LoopNextQuestionListItem
                    {
                        QuestionId = question.Id,
                        AnswerId = answer.Id,
                        SurveyNextQuestionId = answer.SurveyNextQuestionId
                    });
                    Traverse(answer.SurveyNextQuestionId);
                }
            }

            Traverse(query.SurveyNextQuestionId);

            // Only return items where QuestionId == query.SurveyQuestionId
            var filtered =  result.Where(x => x.QuestionId == query.SurveyQuestionId).ToList();
            return await Task.FromResult(QueryResult.Create(filtered));
        }
    }

    public class LoopNextQuestionListItem
    {
        public Guid QuestionId { get; set; }
        public Guid AnswerId { get; set; }
        public Guid? SurveyNextQuestionId { get; set; }
    }
}
