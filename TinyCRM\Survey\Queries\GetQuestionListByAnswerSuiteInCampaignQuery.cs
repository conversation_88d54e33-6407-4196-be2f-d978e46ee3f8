﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetQuestionListByAnswerSuiteInCampaignQuery : QueryBase<SurveyQuestionData>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetQuestionListByAnswerSuiteInCampaignQueryHandler : QueryHandlerBase<GetQuestionListByAnswerSuiteInCampaignQuery, SurveyQuestionData>
    {
        public GetQuestionListByAnswerSuiteInCampaignQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionData>> ExecuteAsync(GetQuestionListByAnswerSuiteInCampaignQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd ,"@CampaignId", query.CampaignId)
            });

            cmd.CommandText = "GetQuestionListByAnswerSuiteInCampaign";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<SurveyQuestionData>(cmd);
            return new QueryResult<SurveyQuestionData>(mainQuery);
        }
    }
}