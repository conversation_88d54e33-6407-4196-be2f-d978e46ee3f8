﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurrveyAnswerListInSuiteQuery : QueryBase<SurveyAnswerData>
    {
        public Guid SurveyAnswerSuiteId { get; set; }
    }

    internal class GetSurrveyAnswerListInSuiteQueryHandler : QueryHandlerBase<GetSurrveyAnswerListInSuiteQuery, SurveyAnswerData>
    {
        public GetSurrveyAnswerListInSuiteQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyAnswerData>> ExecuteAsync(GetSurrveyAnswerListInSuiteQuery query)
        {
            var surveyAnswerQuery = EntitySet.Get<SurveyAnswerEntity>();
            var surveyAnswerSuiteAnswerQuery = EntitySet.Get<SurveyAnswerSuiteAnswerEntity>();
            var surveyAnswerSuiteQuery = EntitySet.Get<SurveyAnswerSuiteEntity>();

            var mainQuery = (from sa in surveyAnswerQuery
                             join sasa in surveyAnswerSuiteAnswerQuery on sa.Id equals sasa.SurveyAnswerId
                             join sas in surveyAnswerSuiteQuery on sasa.SurveyAnswerSuiteId equals sas.Id
                             where sas.Id == query.SurveyAnswerSuiteId
                             orderby sa.DisplayOrder
                             select sa);

            return await Task.FromResult(QueryResult.Create(mainQuery, query.Pagination, Mapper.Map<SurveyAnswerData>));
        }
    }
}