﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurrveyAnswersByQuestionQuery : QueryBase<SurveyAnswerData>
    {
        public Guid SurveyQuestionId { get; set; }
    }

    internal class GetSurrveyAnswersByQuestionQueryHandler : QueryHandlerBase<GetSurrveyAnswersByQuestionQuery, SurveyAnswerData>
    {
        public GetSurrveyAnswersByQuestionQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyAnswerData>> ExecuteAsync(GetSurrveyAnswersByQuestionQuery query)
        {
            var surveyAnswerQuery = EntitySet.Get<SurveyAnswerEntity>();
            var mainQuery = (from sa in surveyAnswerQuery
                             where sa.SurveyQuestionId == query.SurveyQuestionId
                             orderby sa.DisplayOrder
                             select sa);
            return await Task.FromResult(QueryResult.Create(mainQuery, query.Pagination, Mapper.Map<SurveyAnswerData>));
        }
    }
}