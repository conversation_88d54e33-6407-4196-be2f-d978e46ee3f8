﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurrveyQuestionItemsBySurveyIdQuery : QueryBase<SurveyQuestionData>
    {
        public Guid SurveyId { get; set; }
    }

    internal class GetSurrveyQuestionItemsBySurveyIdQueryHandler : QueryHandlerBase<GetSurrveyQuestionItemsBySurveyIdQuery, SurveyQuestionData>
    {
        public GetSurrveyQuestionItemsBySurveyIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyQuestionData>> ExecuteAsync(GetSurrveyQuestionItemsBySurveyIdQuery query)
        {
            var surveyQuestionQuery = EntitySet.Get<SurveyQuestionEntity>().Where(sq => sq.SurveyId == query.SurveyId).OrderBy(sq => sq.RootFlowOrder);
            return await Task.FromResult(QueryResult.Create(surveyQuestionQuery, query.Pagination, Mapper.Map<SurveyQuestionData>));
        }
    }
}