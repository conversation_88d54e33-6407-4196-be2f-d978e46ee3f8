﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurrveyQuestionsBySurveyIdQuery : QueryBase<SurveyQuestionListItem>
    {
        public Guid SurveyId { get; set; }
    }

    internal class GetSurrveyQuestionsBySurveyIdQueryHandler : QueryHandlerBase<GetSurrveyQuestionsBySurveyIdQuery, SurveyQuestionListItem>
    {
        public GetSurrveyQuestionsBySurveyIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionListItem>> ExecuteAsync(GetSurrveyQuestionsBySurveyIdQuery query)
        {
            IEnumerable<SurveyQuestionListItem> surveyQuestionList = null;

            var raw = await (from sq in EntitySet.Get<SurveyQuestionEntity>()
                       join sqs in EntitySet.Get<SurveyQuestionSectionEntity>() on sq.SurveyQuestionSectionId equals sqs.Id into _sq
                       from sqt in _sq.DefaultIfEmpty()
                       join sa in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa.SurveyQuestionId into _sa
                       from sa in _sa.DefaultIfEmpty()
                       join nsq in EntitySet.Get<SurveyQuestionEntity>() on sa.SurveyNextQuestionId equals nsq.Id into _san
                       from san in _san.DefaultIfEmpty()
                       where sq.SurveyId == query.SurveyId
                       orderby sq.RootFlowOrder
                       select new SurveyQuestionAnswerListItemRaw
                       {
                           QuestionId = sq.Id,
                           Question = sq.Question,
                           AnswerType = sq.AnswerType,
                           RootFlowOrder = sq.RootFlowOrder,
                           SurveyId = sq.SurveyId,
                           SurveyAnswerSuiteId = sq.SurveyAnswerSuiteId,

                           SurveyQuestionSectionId = sq.SurveyQuestionSectionId,
                           QuestionSectionName = sqt.SectionName,
                           QuestionSectionOrder = sqt.DisplayOrder,

                           AnswerId = sa.Id,
                           Answer = sa.Answer,
                           IconId = sa.IconId,
                           SurveyQuestionId = sa.SurveyQuestionId,
                           SurveyNextQuestionId = sa.SurveyNextQuestionId,
                           DisplayOrder = sa.DisplayOrder,
                           SurveyNextQuestion = san.Question
                       }).ToListAsync();

            surveyQuestionList = (from sq in raw
                                  select new SurveyQuestionListItem
                                  {
                                      Id = sq.QuestionId,
                                      Question = sq.Question,
                                      AnswerType = sq.AnswerType,
                                      RootFlowOrder = sq.RootFlowOrder,
                                      SurveyId = sq.SurveyId,
                                      SurveyAnswerSuiteId = sq.SurveyAnswerSuiteId,

                                      SurveyQuestionSectionId = sq.SurveyQuestionSectionId,
                                      QuestionSectionName = sq.QuestionSectionName,
                                      QuestionSectionOrder = sq.QuestionSectionOrder,

                                      SurveyAnswerItems = (from sa in raw
                                                           where sa.SurveyQuestionId == sq.QuestionId
                                                           orderby sa.DisplayOrder
                                                           select new SurveyAnswerItem
                                                           {
                                                               Id = sa.AnswerId.Value,
                                                               Answer = sa.Answer,
                                                               IconId = sa.IconId,
                                                               DisplayOrder = sa.DisplayOrder,
                                                               SurveyQuestionId = sa.SurveyQuestionId.Value,
                                                               SurveyNextQuestionId = sa.SurveyNextQuestionId,
                                                               SurveyNextQuestion = sa.SurveyNextQuestion
                                                           }).ToList()
                                  }).GroupBy(x => x.Id).Select(g => g.First()).ToList();

            return QueryResult.Create(surveyQuestionList);
        }

        internal class SurveyQuestionAnswerListItemRaw
        {
            public Guid QuestionId { get; set; }

            public string Question { get; set; }

            public Guid? SurveyQuestionSectionId { get; set; }

            public string QuestionSectionName { get; set; }

            public int? QuestionSectionOrder { get; set; }

            public AnswerType AnswerType { get; set; }

            public double? RootFlowOrder { get; set; }

            public Guid? SurveyAnswerSuiteId { get; set; }

            public Guid SurveyId { get; set; }

            public Guid? AnswerId { get; set; }

            public string Answer { get; set; }

            public Guid? IconId { get; set; }

            public Guid? SurveyQuestionId { get; set; }

            public Guid? SurveyNextQuestionId { get; set; }

            public int? DisplayOrder { get; set; }

            public string SurveyNextQuestion { get; set; }
        }
    }
}