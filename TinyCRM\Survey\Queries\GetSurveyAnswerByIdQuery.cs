﻿using AutoMapper;
using CuttingEdge.Conditions;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyAnswerByIdQuery : QueryBase<SurveyAnswerData>
    {
        public Guid Id { get; set; }
    }

    internal class GetSurveyAnswerByIdQueryHandler : QueryHandlerBase<GetSurveyAnswerByIdQuery, SurveyAnswerData>
    {
        public GetSurveyAnswerByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyAnswerData>> ExecuteAsync(GetSurveyAnswerByIdQuery query)
        {
            var surveyAnswer = await EntitySet.GetAsync<SurveyAnswerEntity>(query.Id);
            return new QueryResult<SurveyAnswerData>(Mapper.Map<SurveyAnswerData>(surveyAnswer));
        }
    }
}