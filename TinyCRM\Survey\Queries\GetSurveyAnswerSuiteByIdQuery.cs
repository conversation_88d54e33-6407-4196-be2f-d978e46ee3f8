﻿using System;
using System.Linq;
using AutoMapper;
using CuttingEdge.Conditions;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyAnswerSuiteByIdQuery : QueryBase<SurveyAnswerSuiteData>
    {
        public Guid Id { get; set; }
    }

    internal class GetSurveyAnswerSuiteByIdQueryHandler : QueryHandlerBase<GetSurveyAnswerSuiteByIdQuery, SurveyAnswerSuiteData>
    {
        public GetSurveyAnswerSuiteByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyAnswerSuiteData>> ExecuteAsync(GetSurveyAnswerSuiteByIdQuery query)
        {
            var surveyAnswerSuite = await EntitySet.GetAsync<SurveyAnswerSuiteEntity>(query.Id);
            return new QueryResult<SurveyAnswerSuiteData>(Mapper.Map<SurveyAnswerSuiteData>(surveyAnswerSuite));
        }
    }
}