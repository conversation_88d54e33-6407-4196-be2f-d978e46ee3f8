﻿using AutoMapper;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyAnswerSuiteListQuery : QueryBase<SurveyAnswerSuiteData>
    {
        public string SearchText { get; set; }
    }

    internal class GetSurveyAnswerSuiteListQueryHandler : QueryHandlerBase<GetSurveyAnswerSuiteListQuery, SurveyAnswerSuiteData>
    {
        public GetSurveyAnswerSuiteListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyAnswerSuiteData>> ExecuteAsync(GetSurveyAnswerSuiteListQuery query)
        {
            var surveyAnswerSuiteQuery = await EntitySet.GetAsync<SurveyAnswerSuiteEntity>();
            if (query.SearchText.IsNotNullOrEmpty())
            {
                surveyAnswerSuiteQuery = surveyAnswerSuiteQuery.Where(sas => sas.Description.Contains(query.SearchText));
            }
            return QueryResult.Create(surveyAnswerSuiteQuery, query.Pagination, x => Mapper.Map<SurveyAnswerSuiteData>(x));
        }
    }
}