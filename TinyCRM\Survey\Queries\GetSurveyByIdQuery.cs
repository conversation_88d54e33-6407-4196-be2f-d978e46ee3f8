﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyByIdQuery : QueryBase<SurveyData>
    {
        public Guid Id { get; set; }
    }

    internal class GetSurveyByIdQueryHandler : QueryHandlerBase<GetSurveyByIdQuery, SurveyData>
    {
        public GetSurveyByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyData>> ExecuteAsync(GetSurveyByIdQuery query)
        {
            var survey = await EntitySet.GetAsync<SurveyEntity>(query.Id);
            return new QueryResult<SurveyData>(Mapper.Map<SurveyData>(survey));
        }
    }
}