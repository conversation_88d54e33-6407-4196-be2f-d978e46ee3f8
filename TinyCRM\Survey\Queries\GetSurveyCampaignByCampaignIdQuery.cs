﻿using AutoMapper;
using CuttingEdge.Conditions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignByCampaignIdQuery : QueryBase<SurveyCampaignData>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetSurveyCampaignByCampaignIdQueryHandler : QueryHandlerBase<GetSurveyCampaignByCampaignIdQuery, SurveyCampaignData>
    {
        public GetSurveyCampaignByCampaignIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyCampaignData>> ExecuteAsync(GetSurveyCampaignByCampaignIdQuery query)
        {
            var surveyCampaignQuery = await EntitySet.GetAsync<SurveyCampaignEntity>();
            var result = await surveyCampaignQuery.FirstOrDefaultAsync(x => x.CampaignId == query.CampaignId);
            return new QueryResult<SurveyCampaignData>(Mapper.Map<SurveyCampaignData>(result));
        }
    }
}
