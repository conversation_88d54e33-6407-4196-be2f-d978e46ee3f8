﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignByIdQuery : QueryBase<SurveyCampaignData>
    {
        public Guid Id { get; set; }

        public GetSurveyCampaignByIdQuery(Guid id)
        {
            Id = id;
        }
    }

    internal class GetSurveyCampaignByIdQueryHandler : QueryHandlerBase<GetSurveyCampaignByIdQuery, SurveyCampaignData>
    {
        public GetSurveyCampaignByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyCampaignData>> ExecuteAsync(GetSurveyCampaignByIdQuery query)
        {
            var surveyCampaignQuery = EntitySet.Get<SurveyCampaignEntity>();
            var result = surveyCampaignQuery.FirstOrDefault(x => x.Id == query.Id);
            return await Task.FromResult(new QueryResult<SurveyCampaignData>(Mapper.Map<SurveyCampaignData>(result)));
        }
    }
}
