﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using TinyCRM.Campaign;
using TinyCRM.Enums;
using TinyCRM.Outbound.Campaign;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignByServiceTypeQuery : QueryBase<SurveyCampaignData>
    {
        public Guid ServiceTypeId
        {
            get;
            set;
        }

        public SurveyEvent SurveyEvent
        {
            get; set;
        }
    }

    internal class GetSurveyCampaignByServiceTypeQueryHandler : QueryHandlerBase<GetSurveyCampaignByServiceTypeQuery, SurveyCampaignData>
    {
        public GetSurveyCampaignByServiceTypeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyCampaignData>> ExecuteAsync(GetSurveyCampaignByServiceTypeQuery query)
        {
            //var cmd = EntitySet.CreateDbCommand();
            //cmd.Parameters.AddRange(new[]
            //{
            //    DbParameterHelper.AddNullableEnum(cmd, "@SurveyEvent", query.SurveyEvent),
            //    DbParameterHelper.AddNullableGuid(cmd, "@ServiceTypeId", query.ServiceTypeId),
            //    DbParameterHelper.NewNullableDateTimeParameter(cmd, "@GetDate", DateTime.Now.Date)
            //});
            //cmd.CommandType = CommandType.Text;
            //cmd.CommandText = @"
            //SELECT	sc.*
            //FROM	dbo.Campaign c WITH(NOLOCK)
		          //  JOIN dbo.SurveyCampaign sc WITH(NOLOCK) ON sc.CampaignId = c.Id
		          //  JOIN dbo.SurveyCampaignExecution sce WITH(NOLOCK) ON sce.SurveyCampaignId = sc.Id
            //WHERE	c.Deleted = 0 AND sc.Deleted = 0 AND sce.Deleted = 0
		          //  AND c.Status = 2	-- Running
		          //  AND sc.SurveyType = 1	-- Automatic
		          //  AND sce.Event = @SurveyEvent
		          //  AND sce.ServiceTypeId = @ServiceTypeId
		          //  AND (c.StartDate IS NULL OR c.StartDate <= @GetDate)
		          //  AND (c.EndDate IS NULL OR DATEADD(DAY, 1, c.EndDate) > @GetDate)
            //ORDER BY sc.CreatedDate DESC";

            //var mainQuery = await EntitySet.ExecuteReadCommandAsync<SurveyCampaignData>(cmd);
            //return new QueryResult<SurveyCampaignData>(mainQuery);

            var today = DateTime.Now.Date;
            var result = from c in EntitySet.Get<CampaignEntity>()
                         join sc in EntitySet.Get<SurveyCampaignEntity>() on c.Id equals sc.CampaignId
                         join sce in EntitySet.Get<SurveyCampaignExecutionEntity>() on sc.Id equals sce.SurveyCampaignId
                         where !c.Deleted && !sc.Deleted && !sce.Deleted
                               && c.Status == CampaignStatus.Running
                               && sc.SurveyType == SurveyType.Automatic
                               && sce.Event == query.SurveyEvent
                               && sce.ServiceTypeId == query.ServiceTypeId
                               && (c.StartDate == null || c.StartDate <= today)
                               && (c.EndDate == null || c.EndDate.Value.AddDays(1) > today)
                         orderby sc.CreatedDate descending
                         select new SurveyCampaignData
                         {
                             Id = sc.Id,
                             SurveyType = sc.SurveyType,
                             SurveyId = sc.SurveyId,
                             CampaignId = sc.CampaignId,
                             SmsContent = sc.SmsContent,
                             InvitationEmailTitle = sc.InvitationEmailTitle,
                             InvitationEmailTemplate = sc.InvitationEmailTemplate,
                             Target = sc.Target,
                             CreatedDate = sc.CreatedDate,
                             CreatedBy = sc.CreatedBy,
                             ModifiedDate = sc.ModifiedDate,
                             ModifiedBy = sc.ModifiedBy,
                             Deleted = sc.Deleted,
                             DeletedDate = sc.DeletedDate,
                             DeletedBy = sc.DeletedBy
                         };
            return await Task.FromResult(QueryResult.Create(result));
        }
    }
}