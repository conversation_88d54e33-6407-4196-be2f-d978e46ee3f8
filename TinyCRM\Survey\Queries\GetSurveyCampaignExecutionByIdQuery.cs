﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignExecutionByIdQuery : QueryBase<SurveyCampaignExecutionData>
    {
        public Guid Id { get; set; }

        public GetSurveyCampaignExecutionByIdQuery(Guid id)
        {
            Id = id;
        }
    }

    internal class GetSurveyCampaignExecutionQueryHandler :
        QueryHandlerBase<GetSurveyCampaignExecutionByIdQuery, SurveyCampaignExecutionData>
    {
        public GetSurveyCampaignExecutionQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyCampaignExecutionData>> ExecuteAsync(GetSurveyCampaignExecutionByIdQuery query)
        {
            var surveyCampaignExecutionQuery = EntitySet.Get<SurveyCampaignExecutionEntity>();
            var result = surveyCampaignExecutionQuery.FirstOrDefault(x => x.Id == query.Id);
            return await Task.FromResult(new QueryResult<SurveyCampaignExecutionData>(Mapper.Map<SurveyCampaignExecutionData>(result)));
        }
    }
}
