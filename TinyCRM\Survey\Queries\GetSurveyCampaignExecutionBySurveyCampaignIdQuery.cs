﻿using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignExecutionBySurveyCampaignIdQuery : QueryBase<SurveyCampaignExecutionListItem>
    {
        public Guid SurveyCampaignId { get; set; }
    }

    internal class GetSurveyCampaignExecutionBySurveyCampaignIdQueryHandler : QueryHandlerBase<GetSurveyCampaignExecutionBySurveyCampaignIdQuery, SurveyCampaignExecutionListItem>
    {
        public GetSurveyCampaignExecutionBySurveyCampaignIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyCampaignExecutionListItem>> ExecuteAsync(GetSurveyCampaignExecutionBySurveyCampaignIdQuery query)
        {
            var surveyCampaignExecutionQuery = EntitySet.Get<SurveyCampaignExecutionEntity>();
            var result = (from execution in surveyCampaignExecutionQuery
                          join serviceType in EntitySet.Get<ServiceTypeEntity>() on execution.ServiceTypeId equals serviceType.Id
                          join serviceCatLevel1 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level1Id equals serviceCatLevel1.Id
                          join serviceCatLevel2 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level2Id equals serviceCatLevel2.Id into serviceCat2
                          from serviceCatLevel2x in serviceCat2.DefaultIfEmpty()
                          join serviceCatLevel3 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level3Id equals serviceCatLevel3.Id into serviceCat3
                          from serviceCatLevel3x in serviceCat3.DefaultIfEmpty()
                          join serviceCatLevel4 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level4Id equals serviceCatLevel4.Id into serviceCat4
                          from serviceCatLevel4x in serviceCat4.DefaultIfEmpty()
                          where execution.SurveyCampaignId == query.SurveyCampaignId
                          select new SurveyCampaignExecutionListItem
                          {
                              Id = execution.Id,
                              Event = execution.Event,
                              ServiceTypeId = execution.ServiceTypeId,
                              SurveyCampaignId = execution.SurveyCampaignId,
                              Level1Id = serviceCatLevel1.Id,
                              Level1 = serviceCatLevel1.Name,
                              Level2Id = serviceCatLevel2x.Id,
                              Level2 = serviceCatLevel2x.Name,
                              Level3Id = serviceCatLevel3x.Id,
                              Level3 = serviceCatLevel3x.Name,
                              Level4Id = serviceCatLevel4x.Id,
                              Level4 = serviceCatLevel4x.Name,
                          });
            return await Task.FromResult(new QueryResult<SurveyCampaignExecutionListItem>(result, query.Pagination));
        }
    }
}
