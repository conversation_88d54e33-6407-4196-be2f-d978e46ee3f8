﻿using AutoMapper;
using System;
using System.Linq;
using TinyCRM.ServiceCategory;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignExecutionResponseBySurveyCampaignIdQuery : QueryBase<SurveyCampaignResponseExecutionListItem>
    {
        public Guid SurveyCampaignId { get; set; }
    }

    internal class GetSurveyCampaignExecutionResponseBySurveyCampaignIdQueryHandler : QueryHandlerBase<GetSurveyCampaignExecutionResponseBySurveyCampaignIdQuery, SurveyCampaignResponseExecutionListItem>
    {
        public GetSurveyCampaignExecutionResponseBySurveyCampaignIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyCampaignResponseExecutionListItem>> ExecuteAsync(GetSurveyCampaignExecutionResponseBySurveyCampaignIdQuery query)
        {            
            var result = (from execution in EntitySet.Get<SurveyCampaignResponseExecutionEntity>()
                          join serviceType in EntitySet.Get<ServiceTypeEntity>() on execution.ServiceTypeId equals serviceType.Id

                          join serviceCatLevel1 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level1Id equals serviceCatLevel1.Id
                          join serviceCatLevel2 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level2Id equals serviceCatLevel2.Id into serviceCat2
                          from serviceCatLevel2x in serviceCat2.DefaultIfEmpty()
                          join serviceCatLevel3 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level3Id equals serviceCatLevel3.Id into serviceCat3
                          from serviceCatLevel3x in serviceCat3.DefaultIfEmpty()
                          join serviceCatLevel4 in EntitySet.Get<ServiceCategoryEntity>() on serviceType.Level4Id equals serviceCatLevel4.Id into serviceCat4
                          from serviceCatLevel4x in serviceCat4.DefaultIfEmpty()

                          join to in EntitySet.Get<AspNetUserEntity>() on execution.TicketOwnerId equals to.Id into ticketOwner
                          from ticketOwnerx in ticketOwner.DefaultIfEmpty()

                          join question in EntitySet.Get<SurveyQuestionEntity>() on execution.SurveyQuestionId equals question.Id
                          join answer in EntitySet.Get<SurveyAnswerEntity>() on execution.SurveyAnswerId equals answer.Id

                          where execution.SurveyCampaignId == query.SurveyCampaignId
                          select new SurveyCampaignResponseExecutionListItem
                          {
                              Id = execution.Id,
                              ServiceTypeId = execution.ServiceTypeId,
                              SurveyCampaignId = execution.SurveyCampaignId,

                              SurveyQuestionId = execution.SurveyQuestionId,
                              SurveyQuestion = question.Question,

                              SurveyAnswerId = execution.SurveyAnswerId,
                              SurveyAnswer = answer.Answer,

                              Level1Id = serviceCatLevel1.Id,
                              Level1 = serviceCatLevel1.Name,

                              Level2Id = serviceCatLevel2x.Id,
                              Level2 = serviceCatLevel2x.Name,

                              Level3Id = serviceCatLevel3x.Id,
                              Level3 = serviceCatLevel3x.Name,

                              Level4Id = serviceCatLevel4x.Id,
                              Level4 = serviceCatLevel4x.Name,

                              TicketOwnerId = execution.TicketOwnerId,
                              TicketOwner = ticketOwnerx.FullName
                          });
            return new QueryResult<SurveyCampaignResponseExecutionListItem>(result, query.Pagination);
        }
    }
}
