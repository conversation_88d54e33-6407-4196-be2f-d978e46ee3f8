﻿using AutoMapper;
using System;
using System.Linq;
using TinyCRM.Campaign;
using TinyCRM.Outbound.Campaign;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignItemListQuery : QueryBase<SurveyCampaignListItem>
    {
    }

    internal class GetSurveyCampaignItemListQueryHandler : QueryHandlerBase<GetSurveyCampaignItemListQuery, SurveyCampaignListItem>
    {
        public GetSurveyCampaignItemListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyCampaignListItem>> ExecuteAsync(GetSurveyCampaignItemListQuery query)
        {
            var mainQuery = (from camp in EntitySet.Get<CampaignEntity>()
                             join sc in EntitySet.Get<SurveyCampaignEntity>() on camp.Id equals sc.CampaignId
                             orderby sc.CreatedDate descending
                             select new SurveyCampaignListItem
                             {
                                 Id = sc.Id,
                                 Name = camp.CampaignName,
                                 SurveyType = sc.SurveyType,
                                 SurveyId = sc.SurveyId,
                                 StartDate = camp.StartDate,
                                 EndDate = camp.EndDate,
                                 Status = camp.Status,
                                 SmsContent = sc.SmsContent,
                             });

            return QueryResult.Create(mainQuery, query.Pagination);
        }
    }
}
