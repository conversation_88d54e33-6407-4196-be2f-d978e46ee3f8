﻿using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Campaign;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;
using TinyCRM.Outbound.Campaign;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignQuery : QueryBase<SurveyCampaignData>
    {
        public string Name { get; set; }
        public Guid? ServiceTypeId { get; set; }
        public bool Enabled { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public SurveyType? Type { get; set; }
    }

    internal class GetSurveyCampaignQueryHandler : QueryHandlerBase<GetSurveyCampaignQuery, SurveyCampaignData>
    {
        public GetSurveyCampaignQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyCampaignData>> ExecuteAsync(GetSurveyCampaignQuery query)
        {
            var campaignQuery = await EntitySet.GetAsync<CampaignEntity>();
            if (query.Name.IsNotNullOrEmpty())
            {
                campaignQuery = campaignQuery.Where(x => x.CampaignName == query.Name);
            }

            if (query.StartDate.HasValue)
            {
                campaignQuery = campaignQuery.Where(x => x.StartDate >= query.StartDate);
            }

            if (query.EndDate.HasValue)
            {
                campaignQuery = campaignQuery.Where(x => x.EndDate <= query.EndDate.Value);
            }
            var result = (from campaign in campaignQuery
                          join surveyCampaign in EntitySet.Get<SurveyCampaignEntity>() on campaign.Id equals surveyCampaign.CampaignId
                          join surveyCampaignExecution in EntitySet.Get<SurveyCampaignExecutionEntity>() on surveyCampaign.Id
                              equals surveyCampaignExecution.SurveyCampaignId
                          group surveyCampaignExecution by surveyCampaign
                          into g
                          select
                              new SurveyCampaignData
                              {
                                  Id = g.Key.Id,
                                  SurveyType = g.Key.SurveyType,
                                  SurveyId = g.Key.SurveyId,
                                  SmsContent = g.Key.SmsContent,
                                  Target = g.Key.Target,
                                  CampaignId = g.Key.CampaignId
                              }
                );

            if (query.ServiceTypeId.HasValue)
            {
                result = result.Where(x => x.ServiceTypeIds.Contains(query.ServiceTypeId.Value));
            }
            return await Task.FromResult(QueryResult.Create(result, query.Pagination));
        }
    }
}
