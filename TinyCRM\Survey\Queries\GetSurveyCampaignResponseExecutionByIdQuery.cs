﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyCampaignResponseExecutionByIdQuery : QueryBase<SurveyCampaignResponseExecutionData>
    {
        public Guid Id { get; set; }
    }

    internal class GetSurveyCampaignResponseExecutionByIdQueryHandler : QueryHandlerBase<GetSurveyCampaignResponseExecutionByIdQuery, SurveyCampaignResponseExecutionData>
    {
        public GetSurveyCampaignResponseExecutionByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyCampaignResponseExecutionData>> ExecuteAsync(GetSurveyCampaignResponseExecutionByIdQuery query)
        {
            var surveyCampaignResponseExecution = await EntitySet.GetAsync<SurveyCampaignResponseExecutionEntity>(query.Id);
            return new QueryResult<SurveyCampaignResponseExecutionData>(Mapper.Map<SurveyCampaignResponseExecutionData>(surveyCampaignResponseExecution));
        }
    }
}