﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyFeedbackQuery : QueryBase<SurveyFeedbackData>
    {
        public string Code { get; set; }
        public Guid? FeedbackId { get; set; }
    }

    internal class GetSurveyFeedbackByCodeQueryHandler : QueryHandlerBase<GetSurveyFeedbackQuery, SurveyFeedbackData>
    {
        public GetSurveyFeedbackByCodeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyFeedbackData>> ExecuteAsync(GetSurveyFeedbackQuery query)
        {
            var main = EntitySet.Get<SurveyFeedbackEntity>();
            if (query.FeedbackId.HasValue)
            {
                main = main.Where(x => x.Id == query.FeedbackId);
            }
            if (!string.IsNullOrWhiteSpace(query.Code))
            {
                main = main.Where(x => x.Code == query.Code);
            }
            return await Task.FromResult(QueryResult.Create(main, query.Pagination, Mapper.Map<SurveyFeedbackData>));
        }
    }
}