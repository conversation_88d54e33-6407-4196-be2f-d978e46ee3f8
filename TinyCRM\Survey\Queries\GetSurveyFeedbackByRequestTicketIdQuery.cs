﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using TinyCRM.Outbound.Prospect;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyFeedbackByRequestTicketIdQuery : QueryBase<SurveyFeedbackData>
    {
        public Guid RequestTicketId { get; set; }

        public Guid SurveyId { get; set; }
    }

    internal class GetSurveyFeedbackByRequestTicketIdQueryHandler : QueryHandlerBase<GetSurveyFeedbackByRequestTicketIdQuery, SurveyFeedbackData>
    {
        public GetSurveyFeedbackByRequestTicketIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyFeedbackData>> ExecuteAsync(GetSurveyFeedbackByRequestTicketIdQuery query)
        {
            var surveyFeedback = await (from sf in EntitySet.Get<SurveyFeedbackEntity>()
                                  join p in EntitySet.Get<ProspectEntity>() on sf.Id equals p.ReferenceResultId
                                  where sf.SurveyId == query.SurveyId
                                  && p.ReferenceObjectId == query.RequestTicketId
                                  select sf).SingleOrDefaultAsync();
            return new QueryResult<SurveyFeedbackData>(Mapper.Map<SurveyFeedbackData>(surveyFeedback));
        }
    }
}