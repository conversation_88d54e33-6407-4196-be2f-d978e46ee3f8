﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Campaign;
using TinyCRM.Outbound.Campaign;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyFeedbackExpiredDate : QueryBase<DateTime>
    {
        public string FeedbackCode { get; set; }
    }

    internal class GetSurveyFeedbackExpiredDateHandler : QueryHandlerBase<GetSurveyFeedbackExpiredDate, DateTime>
    {
        public GetSurveyFeedbackExpiredDateHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<DateTime>> ExecuteAsync(GetSurveyFeedbackExpiredDate query)
        {
            var result = from sfb in EntitySet.Get<SurveyFeedbackEntity>()
                         join pa in EntitySet.Get<ProspectAssignmentEntity>() on sfb.Id equals pa.ReferenceResultId
                         join ca in EntitySet.Get<CampaignEntity>() on pa.CampaignId equals ca.Id
                         where sfb.Code == query.FeedbackCode && ca.EndDate.HasValue
                         select ca.EndDate.Value;
            return QueryResult.Create(result);
        }
    }
}
