﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyFeedbackListByCustomerQuery : QueryBase<CustomerSurveyFeedbackItem>
    {
        public Guid CustomerId { get; set; }
    }

    internal class GetSurveyFeedbackListByCustomerQueryHandler : QueryHandlerBase<GetSurveyFeedbackListByCustomerQuery, CustomerSurveyFeedbackItem>
    {
        public GetSurveyFeedbackListByCustomerQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) {}

        public override async Task<QueryResult<CustomerSurveyFeedbackItem>> ExecuteAsync(GetSurveyFeedbackListByCustomerQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "dbo.GetSurveyFeedbackListByCustomer";
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CustomerId", query.CustomerId),
                DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow),
                DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow)
            });

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerSurveyFeedbackItem>(cmd);
            return QueryResult.Create(mainQuery);
        }
    }

    public class CustomerSurveyFeedbackItem
    {
        public Guid CampaignId { get; set; }
        public string CampaignName { get; set; }

        public Guid SurveyId { get; set; }
        public string SurveyName { get; set; }

        public Guid CustomerId { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }

        public DateTime CreatedDate { get; set; }

        public string SurveyFeedbackCode { get; set; }

        public string StatusCode { get; set; }
        public string StatusEnglishDescription { get; set; }
        public string StatusVietnameseDescription { get; set; }

        public int TotalCount { get; set; }
    }
}
