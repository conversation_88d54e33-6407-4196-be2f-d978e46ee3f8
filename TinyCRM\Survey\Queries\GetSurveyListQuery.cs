﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyListQuery : QueryBase<SurveyListItem>
    {
        public string Title { get; set; }
        public Guid? SurveyAnswerSuiteId { get; set; }
    }

    internal class GetSurveyListQueryHandler : QueryHandlerBase<GetSurveyListQuery, SurveyListItem>
    {
        public GetSurveyListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyListItem>> ExecuteAsync(GetSurveyListQuery query)
        {
            var surveyQuery = EntitySet.Get<SurveyEntity>();
            var surveyAnswerSuiteQuery = EntitySet.Get<SurveyAnswerSuiteEntity>();

            if (query.SurveyAnswerSuiteId.HasValue && query.SurveyAnswerSuiteId.Value != Guid.Empty)
            {
                surveyQuery = surveyQuery.Where(sq => sq.SurveyAnswerSuiteId == query.SurveyAnswerSuiteId);
            }

            if (query.Title.IsNotNullOrEmpty())
            {
                surveyQuery = surveyQuery.Where(sq => sq.Title.Contains(query.Title));
            }

            var mainQuery = (from sq in surveyQuery
                             join sas in surveyAnswerSuiteQuery on sq.SurveyAnswerSuiteId equals sas.Id into _sq
                             from sqas in _sq.DefaultIfEmpty()
                             orderby sq.CreatedDate descending
                             select new SurveyListItem
                             {
                                 Id = sq.Id,
                                 Title = sq.Title,
                                 ExpiredTime = sq.ExpiredTime,
                                 SurveyFlowType = sq.SurveyFlowType,
                                 SurveyAnswerSuiteId = sq.SurveyAnswerSuiteId,
                                 SurveyAnswerSuite = sqas.Description
                             });

            return await Task.FromResult(QueryResult.Create(mainQuery, query.Pagination));
        }
    }
}