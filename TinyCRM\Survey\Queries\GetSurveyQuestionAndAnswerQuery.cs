﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyQuestionAndAnswerQuery : QueryBase<GetSurveyQuestionAndAnswerQuery.Result>
    {
        public bool IsGetRoot { get; set; }

        public Guid SurveyId { get; set; }

        public Guid? ParentAnwserId { get; set; }

        public Guid ParentQuestionId { get; set; } //trường hợp answer là free text thì phải có field này

        public class Result
        {
            public int GroupOrder { get; set; }

            public bool IsRoot { get; set; }

            public Guid? AnswerSuiteId { get; set; }

            public SurveyQuestionData SurveyQuestion { get; set; }

            public IEnumerable<SurveyAnswerData> SurveyAnswers { get; set; }
        }
    }

    internal class GetSurveyQuestionAndAnswerQueryHandler : QueryHandlerBase<GetSurveyQuestionAndAnswerQuery, GetSurveyQuestionAndAnswerQuery.Result>
    {
        public GetSurveyQuestionAndAnswerQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        private IEnumerable<GetSurveyQuestionAndAnswerQuery.Result> BackToRootAndNextQuestion(Guid currentQuestionId, Guid surveyId)
        {
            var questionInfo = from cu_q in EntitySet.Get<SurveyQuestionEntity>().Where(x => x.Id == currentQuestionId)
                                join pa_a in EntitySet.Get<SurveyAnswerEntity>() on cu_q.Id equals pa_a.SurveyNextQuestionId
                                join pa_q in EntitySet.Get<SurveyQuestionEntity>() on pa_a.SurveyQuestionId equals pa_q.Id
                                where cu_q.SurveyId == surveyId
                                select new { ParentQuestionId = pa_q.Id, CurrentQFlowOder = cu_q.RootFlowOrder };
            if (questionInfo.Any())
            {
                if (questionInfo.First().CurrentQFlowOder.HasValue)
                {
                    var r = (from sq in EntitySet.Get<SurveyQuestionEntity>().Where(x => x.SurveyId == surveyId && x.RootFlowOrder > questionInfo.First().CurrentQFlowOder)
                             join sa in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa.SurveyQuestionId
                             select new
                             {
                                 SurveyQuestion = sq,
                                 SurveyAnswers = sa
                             }).AsEnumerable();
                    var _result = r.GroupBy(k => k.SurveyQuestion, g => g.SurveyAnswers, (k, g) => new GetSurveyQuestionAndAnswerQuery.Result
                    {
                        SurveyQuestion = Mapper.Map<SurveyQuestionData>(k),
                        SurveyAnswers = g.Where(x => x != null).OrderBy(x => x.DisplayOrder).Select(x => Mapper.Map<SurveyAnswerData>(x))
                    }, new SurveyQuestionComparer());
                    return _result.OrderBy(x => x.SurveyQuestion.RootFlowOrder);
                }
                return BackToRootAndNextQuestion(questionInfo.First().ParentQuestionId, surveyId);
            }
            var current_qa = (from cu_q in EntitySet.Get<SurveyQuestionEntity>().Where(x => x.Id == currentQuestionId)
                              join sqs in EntitySet.Get<SurveyQuestionSectionEntity>() on cu_q.SurveyQuestionSectionId equals sqs.Id into _sqs
                              from sqs in _sqs.DefaultIfEmpty()
                              where cu_q.SurveyId == surveyId
                              select new { cu_q = cu_q.RootFlowOrder, cu_s = sqs.Id == null ? 0 : sqs.DisplayOrder }).First();
            var raw = (from sq in EntitySet.Get<SurveyQuestionEntity>()
                       join sa2 in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa2.SurveyQuestionId into _sa2
                       from sa2 in _sa2.DefaultIfEmpty()
                       join su in EntitySet.Get<SurveyAnswerSuiteEntity>() on sq.SurveyAnswerSuiteId equals su.Id into _su
                       from su in _su.DefaultIfEmpty()
                       join sua in EntitySet.Get<SurveyAnswerSuiteAnswerEntity>() on su.Id equals sua.SurveyAnswerSuiteId into _sua
                       from sua in _sua.DefaultIfEmpty()
                       join sa in EntitySet.Get<SurveyAnswerEntity>() on sua.SurveyAnswerId equals sa.Id into _sa
                       from sa in _sa.DefaultIfEmpty()
                       join sqs in EntitySet.Get<SurveyQuestionSectionEntity>() on sq.SurveyQuestionSectionId equals sqs.Id into _sqs
                       from sqs in _sqs.DefaultIfEmpty()
                       where sq.SurveyId == surveyId && ((sqs.Id == null ? 0 : sqs.DisplayOrder) > current_qa.cu_s || (sqs.Id == null ? 0 : sqs.DisplayOrder) == current_qa.cu_s && sq.RootFlowOrder > current_qa.cu_q)
                       select new
                       {
                           GroupOrder = sqs.Id == null ? 0 : sqs.DisplayOrder,
                           SurveyQuestion = sq,
                           SurveyAnswers = sa.Id == null ? sa2 : sa
                       }).AsEnumerable();
            var result = raw.GroupBy(k => k.SurveyQuestion, g => g , (k, g) => new GetSurveyQuestionAndAnswerQuery.Result
            {
                GroupOrder = g.First().GroupOrder,
                SurveyQuestion = Mapper.Map<SurveyQuestionData>(k),
                SurveyAnswers = g.Where(x => x != null).Select(x => x.SurveyAnswers).Where(x => x != null).OrderBy(x => x.DisplayOrder).Select(x => Mapper.Map<SurveyAnswerData>(x))
            }, new SurveyQuestionComparer());
            return result.OrderBy(x => x.GroupOrder).ThenBy(x => x.SurveyQuestion.RootFlowOrder);
        }

        public override async Task<QueryResult<GetSurveyQuestionAndAnswerQuery.Result>> ExecuteAsync(GetSurveyQuestionAndAnswerQuery query)
        {
            IEnumerable<GetSurveyQuestionAndAnswerQuery.Result> result;
            if (query.IsGetRoot)
            {
                var survey = await EntitySet.GetAsync<SurveyEntity>(query.SurveyId);
                if (survey.SurveyFlowType == SurveyFlowType.StepByStep)
                {
                    var raw = (from sq in EntitySet.Get<SurveyQuestionEntity>()
                               join sa2 in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa2.SurveyQuestionId into _sa2
                               from sa2 in _sa2.DefaultIfEmpty()
                               join su in EntitySet.Get<SurveyAnswerSuiteEntity>() on sq.SurveyAnswerSuiteId equals su.Id into _su
                               from su in _su.DefaultIfEmpty()
                               join sua in EntitySet.Get<SurveyAnswerSuiteAnswerEntity>() on su.Id equals sua.SurveyAnswerSuiteId into _sua
                               from sua in _sua.DefaultIfEmpty()
                               join sa in EntitySet.Get<SurveyAnswerEntity>() on sua.SurveyAnswerId equals sa.Id into _sa
                               from sa in _sa.DefaultIfEmpty()
                               join sqs in EntitySet.Get<SurveyQuestionSectionEntity>() on sq.SurveyQuestionSectionId equals sqs.Id into _sqs
                               from sqs in _sqs.DefaultIfEmpty()
                               where sq.SurveyId == query.SurveyId && sq.RootFlowOrder.HasValue
                               select new
                               {
                                   GroupOrder = (int?)sqs.DisplayOrder,
                                   SurveyQuestion = sq,
                                   SurveyAnswers = sa.Id == null ? sa2 : sa
                               }).AsEnumerable();
                    result = raw.GroupBy(k => k.SurveyQuestion, g => g, (k, g) => new GetSurveyQuestionAndAnswerQuery.Result
                    {
                        GroupOrder = g.First().GroupOrder??0,
                        IsRoot = true,
                        SurveyQuestion = Mapper.Map<SurveyQuestionData>(k),
                        SurveyAnswers = g.Where(x => x != null && x.SurveyAnswers != null).Select(x => x.SurveyAnswers).OrderBy(x => x.DisplayOrder).Select(x => Mapper.Map<SurveyAnswerData>(x))
                    }, new SurveyQuestionComparer()).OrderBy(x => x.GroupOrder).ThenBy(x => x.SurveyQuestion.RootFlowOrder);
                }
                else
                {
                    var raw = (from sq in EntitySet.Get<SurveyQuestionEntity>()
                               join su in EntitySet.Get<SurveyAnswerSuiteEntity>() on sq.SurveyAnswerSuiteId equals su.Id into _su
                               from su in _su.DefaultIfEmpty()
                               join sua in EntitySet.Get<SurveyAnswerSuiteAnswerEntity>() on su.Id equals sua.SurveyAnswerSuiteId into _sua
                               from sua in _sua.DefaultIfEmpty()
                               join sa in EntitySet.Get<SurveyAnswerEntity>() on sua.SurveyAnswerId equals sa.Id into _sa
                               from sa in _sa.DefaultIfEmpty()
                               where sq.SurveyId == query.SurveyId
                               select new
                               {
                                   AnswerSuiteId = (Guid?)su.Id,
                                   SurveyQuestion = sq,
                                   SurveyAnswers = sa
                               }).AsEnumerable();
                    var with_no_suite = (from _raw in raw.Where(x => !x.AnswerSuiteId.HasValue)
                                             join sa in EntitySet.Get<SurveyAnswerEntity>() on _raw.SurveyQuestion.Id equals sa.SurveyQuestionId into _sa
                                             from sa in _sa.DefaultIfEmpty()
                                             select new
                                             {
                                                 _raw.SurveyQuestion,
                                                 SurveyAnswers = sa
                                             }).AsEnumerable()
                                             .GroupBy(k => k.SurveyQuestion, g => g.SurveyAnswers, (k, g) => new GetSurveyQuestionAndAnswerQuery.Result
                                             {
                                                 AnswerSuiteId = null,
                                                 IsRoot = true,
                                                 SurveyQuestion = Mapper.Map<SurveyQuestionData>(k),
                                                 SurveyAnswers = g.Where(x => x != null).OrderBy(x => x.DisplayOrder).Select(x => Mapper.Map<SurveyAnswerData>(x))
                                             }, new SurveyQuestionComparer());

                    result = raw.Where(x => x.AnswerSuiteId.HasValue).GroupBy(k => k.SurveyQuestion, g => new { g.SurveyAnswers, g.AnswerSuiteId }, (k, g) => new GetSurveyQuestionAndAnswerQuery.Result
                    {
                        AnswerSuiteId = g.First().AnswerSuiteId,
                        IsRoot = true,
                        SurveyQuestion = Mapper.Map<SurveyQuestionData>(k),
                        SurveyAnswers = g.Where(x => x != null).Where(x => x.SurveyAnswers != null).Select(x => x.SurveyAnswers).OrderBy(x => x.DisplayOrder).Select(x => Mapper.Map<SurveyAnswerData>(x))
                    }, new SurveyQuestionComparer()).Concat(with_no_suite);
                }
                return await Task.FromResult(QueryResult.Create(result));
            }
            //tìm theo parent - chỉ cho step by step
            if (!query.ParentAnwserId.HasValue)
            {
                // câu trả lời dạng text do KH nhập vào, back về root flow
                return await Task.FromResult(QueryResult.Create(BackToRootAndNextQuestion(query.ParentQuestionId, query.SurveyId).Select(x => { x.IsRoot = true; return x; })));
            }
            var next = (from parent in EntitySet.Get<SurveyAnswerEntity>()
                        join sq in EntitySet.Get<SurveyQuestionEntity>() on parent.SurveyNextQuestionId equals sq.Id
                        join sa in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa.SurveyQuestionId into _sa
                        from sa in _sa.DefaultIfEmpty()
                        join su in EntitySet.Get<SurveyAnswerSuiteEntity>() on sq.SurveyAnswerSuiteId equals su.Id into _su
                        from su in _su.DefaultIfEmpty()
                        join sua in EntitySet.Get<SurveyAnswerSuiteAnswerEntity>() on su.Id equals sua.SurveyAnswerSuiteId into _sua
                        from sua in _sua.DefaultIfEmpty()
                        join sa2 in EntitySet.Get<SurveyAnswerEntity>() on sua.SurveyAnswerId equals sa2.Id into _sa2
                        from sa2 in _sa2.DefaultIfEmpty()
                        where parent.Id == query.ParentAnwserId && sq.SurveyId == query.SurveyId
                        select new
                        {
                            SurveyQuestion = sq,
                            SurveyAnswers = sa2 != null ? sa2 : sa
                        }).AsEnumerable();
            if (!next.Any())
            {
                var parentQuestionId = query.ParentQuestionId;
                result = BackToRootAndNextQuestion(parentQuestionId, query.SurveyId);
                result = result.Select(x => { x.IsRoot = true; return x; });
            }
            else
            {
                result = next.GroupBy(k => k.SurveyQuestion, g => g.SurveyAnswers, (k, g) => new GetSurveyQuestionAndAnswerQuery.Result
                {
                    IsRoot = false,
                    SurveyQuestion = Mapper.Map<SurveyQuestionData>(k),
                    SurveyAnswers = g.Where(x => x != null).OrderBy(x => x.DisplayOrder).Select(x => Mapper.Map<SurveyAnswerData>(x))
                }, new SurveyQuestionComparer());
            }
            return await Task.FromResult(QueryResult.Create(result));
        }
    }
}