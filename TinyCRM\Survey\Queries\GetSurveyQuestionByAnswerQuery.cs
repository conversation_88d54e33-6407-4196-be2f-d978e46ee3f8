﻿using AutoMapper;
using CuttingEdge.Conditions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyQuestionByAnswerQuery : QueryBase<SurveyQuestionData>
    {
        public Guid AnswerId { get; set; }
    }

    internal class GetSurveyQuestionByAnswerQueryHandler : QueryHandlerBase<GetSurveyQuestionByAnswerQuery, SurveyQuestionData>
    {
        public GetSurveyQuestionByAnswerQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionData>> ExecuteAsync(GetSurveyQuestionByAnswerQuery query)
        {
            var surveyQuestion = await (from sq in EntitySet.Get<SurveyQuestionEntity>()
                                  join sa in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa.SurveyQuestionId
                                  where sa.Id == query.AnswerId
                                  select sq).SingleOrDefaultAsync();
            return new QueryResult<SurveyQuestionData>(Mapper.Map<SurveyQuestionData>(surveyQuestion));
        }
    }
}