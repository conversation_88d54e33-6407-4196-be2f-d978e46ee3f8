﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyQuestionByIdQuery : QueryBase<SurveyQuestionData>
    {
        public Guid Id { get; set; }
    }

    internal class GetSurveyQuestionByIdQueryHandler : QueryHandlerBase<GetSurveyQuestionByIdQuery, SurveyQuestionData>
    {
        public GetSurveyQuestionByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SurveyQuestionData>> ExecuteAsync(GetSurveyQuestionByIdQuery query)
        {
            var surveyQuestion = await EntitySet.GetAsync<SurveyQuestionEntity>(query.Id);
            return new QueryResult<SurveyQuestionData>(Mapper.Map<SurveyQuestionData>(surveyQuestion));
        }
    }
}