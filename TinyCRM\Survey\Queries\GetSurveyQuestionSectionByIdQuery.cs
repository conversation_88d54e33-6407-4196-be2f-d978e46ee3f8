﻿using AutoMapper;
using CuttingEdge.Conditions;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyQuestionSectionByIdQuery : QueryBase<SurveyQuestionSectionData>
    {
        public Guid Id { get; set; }
    }

    internal class GetSurveyQuestionSectionByIdQueryHandler : QueryHandlerBase<GetSurveyQuestionSectionByIdQuery, SurveyQuestionSectionData>
    {
        public GetSurveyQuestionSectionByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionSectionData>> ExecuteAsync(GetSurveyQuestionSectionByIdQuery query)
        {
            var surveyQuestionSection = await EntitySet.GetAsync<SurveyQuestionSectionEntity>(query.Id);
            return new QueryResult<SurveyQuestionSectionData>(Mapper.Map<SurveyQuestionSectionData>(surveyQuestionSection));
        }
    }
}