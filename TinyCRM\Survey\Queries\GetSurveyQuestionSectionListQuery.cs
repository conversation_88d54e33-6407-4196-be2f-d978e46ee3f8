﻿using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyQuestionSectionListQuery : QueryBase<SurveyQuestionSectionData>
    {
        public Guid SurveyId { get; set; }
    }

    internal class GetSurveyQuestionSectionListQueryHandler : QueryHandlerBase<GetSurveyQuestionSectionListQuery, SurveyQuestionSectionData>
    {
        public GetSurveyQuestionSectionListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionSectionData>> ExecuteAsync(GetSurveyQuestionSectionListQuery query)
        {
            var surveyQuestionSectionQuery = await EntitySet.Get<SurveyQuestionSectionEntity>()
                .Where(sqs => sqs.SurveyId == query.SurveyId)
                .OrderBy(sqs => sqs.DisplayOrder).ToListAsync();
            return new QueryResult<SurveyQuestionSectionData>(Mapper.Map<SurveyQuestionSectionData>(surveyQuestionSectionQuery));            
        }
    }
}