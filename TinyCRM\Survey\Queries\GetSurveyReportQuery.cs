﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyReportQuery : QueryBase<GetSurveyReportQuery.ReportItem>
    {
        public Guid? TargetId { get; set; }
        public Guid CampaignId { get; set; }
        public Guid? ParentAnswerId { get; set; }

        public class ReportItem
        {
            //public Guid SurveyeeId { get; set; }
            //public string SurveyeeName { get; set; }
            //public string OrganizationName { get; set; }
            public Guid? ParentAnswerId { get; set; }
            public string ParentAnswer { get; set; }
            public Guid QuestionId { get; set; }
            public string Question { get; set; }
            public Guid? AnswerId { get; set; }
            public string Answer { get; set; }
            public Guid? IconId { get; set; }
            public int Count { get; set; }
        }
    }

    internal class GetSurveyReportQueryHandler : QueryHandlerBase<GetSurveyReportQuery, GetSurveyReportQuery.ReportItem>
    {
        public GetSurveyReportQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<GetSurveyReportQuery.ReportItem>> ExecuteAsync(GetSurveyReportQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@TargetId", query.TargetId),
                DbParameterHelper.AddNullableGuid(cmd, "@ParentAnswerId", query.ParentAnswerId),
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId)
            });

            cmd.CommandText = "GetSurveyReport";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<GetSurveyReportQuery.ReportItem>(cmd);
            return new QueryResult<GetSurveyReportQuery.ReportItem>(mainQuery);
        }
    }
}