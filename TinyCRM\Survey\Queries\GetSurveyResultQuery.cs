﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyResultQuery : QueryBase<GetSurveyResultQuery.Result>
    {
        public class FeedbackResult
        {
            public string FeedbackText { get; set; }
            public List<Guid> AnswerList { get; set; }
        }
        public class Result
        {
            public SurveyQuestionSectionEntity QuestionSection { get; set; }
            public SurveyQuestionEntity Question { get; set; }
            public SurveyAnswerSuiteEntity AnswerSuite { get; set; }
            public List<SurveyAnswerEntity> Answers { get; set; }
            public FeedbackResult Feedback { get; set; }
            public Result BranchResult { get; set; }
            public Guid? ParentQuestionId { get; set; }
        }

        public Guid? ProspectAssignmentId { get; set; }
        public Guid FeedbackId { get; set; }
    }

    internal class GetSurveyResultQueryHandle : QueryHandlerBase<GetSurveyResultQuery, GetSurveyResultQuery.Result>
    {
        public GetSurveyResultQueryHandle(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        GetSurveyResultQuery.Result Find(List<GetSurveyResultQuery.Result> list, Guid Id)
        {
            var item = list.Where(x => x.Question.Id == Id);
            if (item.Any())
            {
                return item.First();
            }
            foreach (var i in list.Where(x => x.BranchResult != null))
            {
                var f = Find(new List<GetSurveyResultQuery.Result> { i.BranchResult }, Id);
                if (f != null)
                {
                    return f;
                }
            }
            return null;
        }
        bool AddChild(List<GetSurveyResultQuery.Result> root_list, GetSurveyResultQuery.Result item)
        {
            var parent = Find(root_list, item.ParentQuestionId.Value);
            if (parent == null)
            {
                return false;
            }
            parent.BranchResult = item;
            return true;
        }
        public override async Task<QueryResult<GetSurveyResultQuery.Result>> ExecuteAsync(GetSurveyResultQuery query)
        {
            if (query.ProspectAssignmentId.HasValue)
            {                
                query.FeedbackId = (await EntitySet.GetAsync<ProspectAssignmentEntity>(query.ProspectAssignmentId.Value)).ReferenceResultId ?? query.FeedbackId;                
            }
            var raw = from fba in EntitySet.Get<SurveyFeedbackAnswerEntity>()
                         join sq in EntitySet.Get<SurveyQuestionEntity>() on fba.SurveyQuestionId equals sq.Id

                         join su in EntitySet.Get<SurveyAnswerSuiteEntity>() on sq.SurveyAnswerSuiteId equals su.Id into _su
                         from su in _su.DefaultIfEmpty()
                         join sua in EntitySet.Get<SurveyAnswerSuiteAnswerEntity>() on su.Id equals sua.SurveyAnswerSuiteId into _sua
                         from sua in _sua.DefaultIfEmpty()
                         join sa_sua in EntitySet.Get<SurveyAnswerEntity>() on sua.SurveyAnswerId equals sa_sua.Id into _sa_sua
                         from sa_sua in _sa_sua.DefaultIfEmpty()
                         join sa in EntitySet.Get<SurveyAnswerEntity>() on sq.Id equals sa.SurveyQuestionId into _sa
                         from sa in _sa.DefaultIfEmpty()

                         join sa_sub in EntitySet.Get<SurveyAnswerEntity>() on fba.SurveyAnswerId equals sa_sub.Id into _sa_sub
                         from sa_sub in _sa_sub.DefaultIfEmpty()
                         join pa_sub in EntitySet.Get<SurveyAnswerEntity>() on fba.SurveyAnswerParentId equals pa_sub.Id into _pa_sub
                         from pa_sub in _pa_sub.DefaultIfEmpty()
                         join ps_sub in EntitySet.Get<SurveyQuestionEntity>() on pa_sub.SurveyQuestionId equals ps_sub.Id into _ps_sub
                         from ps_sub in _ps_sub.DefaultIfEmpty()

                         join sqs in EntitySet.Get<SurveyQuestionSectionEntity>() on sq.SurveyQuestionSectionId equals sqs.Id into _sqs
                         from sqs in _sqs.DefaultIfEmpty()
                         where fba.SurveyFeedbackId == query.FeedbackId
                         select new
                         {
                             ParentQuestionId = (Guid?)ps_sub.Id,
                             QuestionSection = sqs,
                             Question = sq,
                             Answer = sa_sua.Id != null ? sa_sua : sa,
                             AnswerSuite = su,
                             FeedbackText = fba.SurveyAnswerText,
                             FeedbackAnswerId = fba.SurveyAnswerId
                         };
            var result_raw = raw.AsEnumerable().GroupBy(k => k.Question, g => new { g.QuestionSection, g.AnswerSuite, g.Answer, g.FeedbackText, g.FeedbackAnswerId, g.ParentQuestionId }, (k, g) => new GetSurveyResultQuery.Result
            {
                ParentQuestionId = g.First().ParentQuestionId,
                Question = k,
                QuestionSection = g.First().QuestionSection,
                AnswerSuite = g.First().AnswerSuite,
                Answers = g.Where(x => x.Answer != null).Select(x => x.Answer).GroupBy(ki => ki.Id, gi => gi, (ki, gi) => gi.First()).ToList(),
                Feedback = new GetSurveyResultQuery.FeedbackResult
                {
                    FeedbackText = g.First().FeedbackText,
                    AnswerList = g.Where(x => x.FeedbackAnswerId.HasValue).Select(x => x.FeedbackAnswerId.Value).Distinct().ToList()
                }
            }, new SurveyQuestionComparer());
            List<GetSurveyResultQuery.Result> result = result_raw.Where(x => x.Question.RootFlowOrder.HasValue).ToList();
            List<GetSurveyResultQuery.Result> left = result_raw.Where(x => !x.Question.RootFlowOrder.HasValue).ToList();

            while (true)
            {
                var rm = new List<GetSurveyResultQuery.Result>();
                foreach (var i in left)
                {
                    if (AddChild(result, i))
                    {
                        rm.Add(i);
                    }
                }
                if (rm.Any())
                {
                    rm.ForEach(x => left.Remove(x));
                }
                if (!left.Any())
                {
                    break;
                }
            }
            return await Task.FromResult(new QueryResult<GetSurveyResultQuery.Result>(result));
        }
    }
}
