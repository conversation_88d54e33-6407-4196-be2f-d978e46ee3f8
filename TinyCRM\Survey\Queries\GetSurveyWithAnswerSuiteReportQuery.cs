﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetSurveyWithAnswerSuiteReportQuery : QueryBase<SurveyQuestionAnswerSuiteReportItem>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetSurveyWithAnswerSuiteReportQueryHandler : QueryHandlerBase<GetSurveyWithAnswerSuiteReportQuery, SurveyQuestionAnswerSuiteReportItem>
    {
        public GetSurveyWithAnswerSuiteReportQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<SurveyQuestionAnswerSuiteReportItem>> ExecuteAsync(GetSurveyWithAnswerSuiteReportQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId)
            });

            cmd.CommandText = "GetSurveyWithAnswerSuiteReport";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<SurveyQuestionAnswerSuiteReportItem>(cmd);
            return new QueryResult<SurveyQuestionAnswerSuiteReportItem>(mainQuery);
        }
    }
}