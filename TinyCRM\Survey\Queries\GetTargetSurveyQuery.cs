﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Campaign;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Survey.Queries
{
    public class GetTargetSurveyQuery : QueryBase<object>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetTargetSurveyQueryHandler : QueryHandlerBase<GetTargetSurveyQuery, object>
    {
        public GetTargetSurveyQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<object>> ExecuteAsync(GetTargetSurveyQuery query)
        {
            var surveyCampaign= await (await EntitySet.GetAsync<SurveyCampaignEntity>()).FirstOrDefaultAsync(x => x.CampaignId == query.CampaignId);            

            if (surveyCampaign.Target == SurveyTarget.TicketProcessor || surveyCampaign.Target == SurveyTarget.TicketCreator)
            {
                var result = (from w in EntitySet.Get<CampaignWorkEntity>()
                              join fb in EntitySet.Get<SurveyFeedbackEntity>() on w.ResultId equals fb.Id
                              join u in EntitySet.Get<AspNetUserEntity>() on fb.SurveyeeId equals u.Id into _u
                              from u in _u.DefaultIfEmpty()
                              select new
                              {
                                  Value = u == null ? Guid.Empty : u.Id,
                                  Text = u == null ? "Không xác định" : u.FullName
                              } as object).Distinct();
                return QueryResult.Create<object>(result);
            }
            return null;
        }
    }
}
