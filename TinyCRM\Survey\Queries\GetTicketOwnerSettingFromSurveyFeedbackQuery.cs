﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class GetTicketOwnerSettingFromSurveyFeedbackQuery : QueryBase<TicketOwnerSettingFromSurveyFeedbackItem>
    {
        public Guid UserId { get; set; }
    }

    internal class GetTicketOwnerSettingFromSurveyFeedbackQueryHandler : QueryHandlerBase<GetTicketOwnerSettingFromSurveyFeedbackQuery, TicketOwnerSettingFromSurveyFeedbackItem>
    {
        public GetTicketOwnerSettingFromSurveyFeedbackQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<TicketOwnerSettingFromSurveyFeedbackItem>> ExecuteAsync(GetTicketOwnerSettingFromSurveyFeedbackQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@UserId", query.UserId)
            });

            cmd.CommandText = "dbo.GetTicketOwnerSettingFromSurveyFeedback";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<TicketOwnerSettingFromSurveyFeedbackItem>(cmd);

            return new QueryResult<TicketOwnerSettingFromSurveyFeedbackItem>(mainQuery);
        }
    }

    public class TicketOwnerSettingFromSurveyFeedbackItem
    {
        public string CampaignName { get; set; }

        public string Question { get; set; }

        public string Answer { get; set; }

        public string Level1 { get; set; }

        public string Level2 { get; set; }

        public string Level3 { get; set; }

        public string Level4 { get; set; }
    }
}