﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Survey.Queries
{
    public class PreviewSurveyResultQuery : QueryBase<object>
    {
        public bool IsGetRaw { get; set; }
        public Guid FeedbackId { get; set; }
    }

    internal class PreviewSurveyResultQueryHandler : QueryHandlerBase<PreviewSurveyResultQuery, object>
    {
        public PreviewSurveyResultQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<object>> ExecuteAsync(PreviewSurveyResultQuery query)
        {
            var result = from fba in EntitySet.Get<SurveyFeedbackAnswerEntity>()
                         join sq in EntitySet.Get<SurveyQuestionEntity>() on fba.SurveyQuestionId equals sq.Id
                         join sa in EntitySet.Get<SurveyAnswerEntity>() on fba.SurveyAnswerId equals sa.Id into _sa
                         from sa in _sa.DefaultIfEmpty()
                         where fba.SurveyFeedbackId == query.FeedbackId
                         orderby sq.RootFlowOrder, sa.DisplayOrder
                         select new
                         {
                             Id = sq.Id,
                             Question = sq.Question,
                             fba.SurveyAnswerText,
                             Answer = sa == null ? "" : sa.Answer,
                             AnswerId = (Guid?)sa.Id
                         };
            var data =  (await result.ToListAsync()).GroupBy(k => k.Id, g => g, (k, g) =>
            {
                if (query.IsGetRaw)
                {
                    return new
                    {
                        QuestionId = k,
                        SurveyAnswerText = g.First().SurveyAnswerText,
                        Answers = g.Where(x => x.AnswerId.HasValue).Select(x => x.AnswerId)
                    } as object;
                }
                else
                {
                    return new
                    {
                        Question = g.First().Question,
                        SurveyAnswerText = g.First().SurveyAnswerText,
                        Answer = " - " + string.Join("<br> - ", g.Select(x => x.Answer))
                    } as object;
                }
            });
            return QueryResult.Create(data);
        }
    }
}
