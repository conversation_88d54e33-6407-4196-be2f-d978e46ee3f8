﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Organization;
using Webaby;
using Webaby.Core.Organization;
using Webaby.Core.UserAccount;
using Webaby.Core.UserAccount.Queries;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Survey.Queries
{
    public class SearchCampaignWorkerQuery : QueryBase<ApplicationUser>
    {
        public Guid CampaignId { get; set; }
        public Guid? OrganizationId { get; set; }
        public string Name { get; set; }
    }

    internal class SearchCampaignWorkerQueryHandler : QueryHandlerBase<SearchCampaignWorkerQuery, ApplicationUser>
    {
        public SearchCampaignWorkerQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }
        public override async Task<QueryResult<ApplicationUser>> ExecuteAsync(SearchCampaignWorkerQuery query)
        {
            var user = from wk in EntitySet.Get<Campaign.CampaignWorkerEntity>().Where(x => x.CampaignId == query.CampaignId)
                       join u in EntitySet.Get<AspNetUserEntity>() on wk.UserId equals u.Id
                       join o in EntitySet.Get<OrganizationEntity>() on u.OrganizationId equals o.Id into _o
                       from o in _o.DefaultIfEmpty()
                       orderby u.Id
                       select new ApplicationUser
                       {
                           Id = u.Id,
                           //AvayaAgentID = u.AvayaAgentID,
                           //eOfficeEnabled = u.eOfficeEnabled,
                           //eOfficeId = u.eOfficeId,
                           FullName = u.FullName,
                           OrganizationId = u.OrganizationId,
                           //OrganizationName = o.Name,
                           PhoneNumber = u.PhoneNumber,
                           //PictureId = u.PictureId,
                           BusinessRole = u.BusinessRole,
                           //TotalCount = 0
                       };
            if (query.OrganizationId.HasValue) user = user.Where(x => x.OrganizationId == query.OrganizationId);
            if (!string.IsNullOrWhiteSpace(query.Name)) user = user.Where(x => x.FullName.Contains(query.Name));
            if (query.Pagination != null) user = user.Skip(query.Pagination.Index * query.Pagination.Size).Take(query.Pagination.Size);
            var count = user.Count();
            //var rs = user.AsEnumerable().Select(x => { x.TotalCount = count; return x; });
            var rs = await user.ToListAsync();
            return QueryResult.Create(rs);
        }
    }
}
