﻿using System;
using System.Collections.Generic;
using AutoMapper;
using TinyCRM.Enums;
using TinyCRM.Outbound.ProspectAssignment;

namespace TinyCRM.Survey.Queries
{
    public class SurveyCampaignData
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public CampaignStatus? Status { get; set; }

        public SurveyType SurveyType { get; set; }

        public Guid SurveyId { get; set; }

        public Guid CampaignId { get; set; }

        public string SmsContent { get; set; }

        public string InvitationEmailTitle { get; set; }

        public string InvitationEmailTemplate { get; set; }

        public SurveyTarget Target { get; set; }

        public IEnumerable<Guid> ServiceTypeIds { get; set; }

        public IEnumerable<string> ServiceTypes { get; set; }

        public IEnumerable<SurveyEvent> Events { get; set; }

        public DateTime CreatedDate { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public Boolean Deleted { get; set; }

        public DateTime? DeletedDate { get; set; }

        public Guid? DeletedBy { get; set; }
    }
}
