﻿using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using TinyCRM.Enums;
using TinyCRM.Phase;
using TinyCRM.Survey.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using TaskStatus = TinyCRM.Enums.TaskStatus;

namespace TinyCRM.TaskType.Queries
{
    public class GetTaskTypeNotificationEventQuery : QueryBase<TaskTypeNotificationEventData>
    {
        public Guid TaskTypeId { get; set; }

        public TaskStatus TaskStatus { get; set; }
    }

    internal class GetTaskTypeNotificationEventQueryHandler : QueryHandlerBase<GetTaskTypeNotificationEventQuery, TaskTypeNotificationEventData>
    {
        public GetTaskTypeNotificationEventQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<TaskTypeNotificationEventData>> ExecuteAsync(GetTaskTypeNotificationEventQuery query)
        {
            var taskTypeNotificationEventEntities = await (from tte in EntitySet.Get<TaskTypeNotificationEventEntity>()
                                                    where tte.TaskTypeId == query.TaskTypeId && query.TaskStatus == query.TaskStatus
                                                    select tte).ToListAsync();
            return new QueryResult<TaskTypeNotificationEventData>(Mapper.Map<TaskTypeNotificationEventData>(taskTypeNotificationEventEntities));                        
        }
    }
}
