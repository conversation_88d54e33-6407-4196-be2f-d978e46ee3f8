﻿using AutoMapper;
using org.apache.zookeeper;
using System;
using Webaby.Core.DueTime.Queries;

namespace TinyCRM.TaskType.Queries
{
    public class TaskTypeData
    {
        public Guid Id { get; set; }

        public string WorkflowName { get; set; }

        public Guid? WorkflowId { get; set; }

        public string TaskType { get; set; }

        public string Code { get; set; }

        public SetBusinessResultMethod SetBusinessResultMethod { get; set; }

        public string AutoSetStatusMethod { get; set; }

        public bool AssignDefaultForOwner { get; set; }

        public int WorkingOrder { get; set; }

        public Guid? AcceptDueTimeId { get; set; }

        public string AcceptDueTime { get; set; }

        public DueTimeInfo AcceptDueTimeInfo { get; set; }

        public Guid? ProcessDueTimeId { get; set; }

        public string ProcessDueTime { get; set; }

        public DueTimeInfo ProcessDueTimeInfo { get; set; }

        public Guid? FbTemplateId { get; set; }

        public Guid? DynamicFormId { get; set; }

        public string ReportShortName { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public DateTime ModifiedDate { get; set; }

        public Boolean Deleted { get; set; }

        public Guid? DeletedBy { get; set; }

        public DateTime? DeletedDate { get; set; }

        public Guid? EmailTemplateForTaskAssignId { get; set; }

        public Guid? EmailTitleTemplateForTaskAssignId { get; set; }

        public CompletedType CompletedType { get; set; }

        public string AssignmentPriorityOrganizationName { get; set; }

        public string AssignmentPriorityUsers { get; set; }

        //Dùng trong create map CreateMap<Source, Destination>().ForMember(dest => dest.SomeProperty, opt => opt.Ignore());
        //[AutoMapper.IgnoreMap]
        public bool hasMultiCondition { get; set; }

        public CopyDynamicFormValueFrom? CopyDynamicFormValueFrom { get; set; }

        public UIType UITaskResultType { get; set; }

        public bool IsDraftingStep { get; set; }

        public bool? AllowManualSLA { get; set; }

        public string HttpTask { get; set; }
    }
}
