﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.TbCallback.Queries
{
    public class GetTbCallbackByReferenceObjectIdQuery : QueryBase<TbCallbackData>
    {
        public Guid ReferenceObjectId { get; set; }
    }

    internal class GetTbCallbackByReferenceObjectIdQueryHandler : QueryHandlerBase<GetTbCallbackByReferenceObjectIdQuery, TbCallbackData>
    {
        public GetTbCallbackByReferenceObjectIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<TbCallbackData>> ExecuteAsync(GetTbCallbackByReferenceObjectIdQuery query)
        {
            var callbackQuery = (await EntitySet.GetAsync<TbCallbackEntity>())
                .Where(cb => cb.ReferenceObjectId == query.ReferenceObjectId)
                .OrderByDescending(cb => cb.CreatedDate);
            return QueryResult.Create(callbackQuery, x => Mapper.Map<TbCallbackData>(x));
        }
    }
}