﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace TinyCRM.TbCallback.Queries
{
    public class GetTbCallbackCallHistoriesQuery : QueryBase<TbCallbackCallInfo>
    {
        public Guid TbCallbackId { get; set; }
    }

    internal class GetTbCallbackCallHistoriesQueryHandler : QueryHandlerBase<GetTbCallbackCallHistoriesQuery, TbCallbackCallInfo>
    {
        public GetTbCallbackCallHistoriesQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<TbCallbackCallInfo>> ExecuteAsync(GetTbCallbackCallHistoriesQuery query)
        {
            var mainQuery =  (from cbc in EntitySet.Get<TbCallbackCallEntity>()
                             join cbcr in EntitySet.Get<TbCallbackCallResultEntity>() on cbc.TbCallbackCallResultId equals cbcr.Id
                             where cbc.CallbackId == query.TbCallbackId
                             orderby cbc.CreatedDate descending
                             select new TbCallbackCallInfo
                             {
                                 Id = cbc.Id,
                                 CallbackId = cbc.CallbackId,
                                 CustomerId = cbc.CustomerId,
                                 PhoneNumber = cbc.PhoneNumber,
                                 TbCallbackCallResultId = cbc.TbCallbackCallResultId,
                                 TbCallbackCallResult = cbcr.CallResult,
                                 Notes = cbc.Notes,
                                 CallId = cbc.CallId,
                                 Duration = cbc.Duration,
                                 CreatedBy = cbc.CreatedBy,
                                 CreatedDate = cbc.CreatedDate
                             });
            var resultQuery = await mainQuery.ToListAsync();
            return QueryResult.Create(resultQuery);            
        }
    }
}