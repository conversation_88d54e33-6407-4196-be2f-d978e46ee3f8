﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.TbCallback.Queries
{
    public class GetTbCallbackCallResultQuery : QueryBase<TbCallbackCallResultData>
    {
        public Guid ReferenceObjectId { get; set; }
    }

    internal class GetTbCallbackCallResultQueryHandler : QueryHandlerBase<GetTbCallbackCallResultQuery, TbCallbackCallResultData>
    {
        public GetTbCallbackCallResultQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<TbCallbackCallResultData>> ExecuteAsync(GetTbCallbackCallResultQuery query)
        {
            var callbackCallResultQuery = (await EntitySet.GetAsync<TbCallbackCallResultEntity>()).OrderBy(cbcr => cbcr.DisplayOrder);
            return QueryResult.Create(callbackCallResultQuery, Mapper.Map<TbCallbackCallResultData>);            
        }
    }
}