﻿using System;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.Access.Queries
    {
    public class GetAccessByIdQuery : QueryBase<AccessData>
    {
        public GetAccessByIdQuery(Guid id)
    {
            Id = id;
        }

        public Guid Id { get; private set; }
    }

    internal class GetAccessByIdQueryHandler : QueryHandlerBase<GetAccessByIdQuery, AccessData>
    {
        public GetAccessByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<AccessData>> ExecuteAsync(GetAccessByIdQuery query)
    {
            var entity = await EntitySet.GetAsync<AccessEntity>(query.Id);
            return new QueryResult<AccessData>(Mapper.Map<AccessData>(entity));
        }
    }
}


