using System;

namespace Webaby.Core.Access.Queries
{
    public class GetAccessByIdQuery : QueryBase<AccessData>
    {
        public GetAccessByIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; private set; }
    }

    internal class GetAccessByIdQueryHandler : QueryHandlerBase<GetAccessByIdQuery, AccessData>
    {
        public override QueryResult<AccessData> Execute(GetAccessByIdQuery query)
        {
            var entity = EntitySet.Get<AccessEntity>(query.Id);
            return new QueryResult<AccessData>(AccessData.FromEntity(entity));
        }
    }
}
