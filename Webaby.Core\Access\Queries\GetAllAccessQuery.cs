﻿namespace Webaby.Core.Access.Queries
    {
    public class GetAllAccessQuery : QueryBase<AccessData>
    {
    }

    internal class GetAllAccessQueryHandler : QueryHandlerBase<GetAllAccessQuery, AccessData>
    {
        public GetAllAccessQueryHandler(
        IEntitySet entitySet,
        IRepository repository,
        IText text,
        IMapper mapper)
      : base(entitySet, repository, text, mapper)
    { }
        public override async Task<QueryResult<AccessData>> ExecuteAsync(GetAllAccessQuery query)
    {
            var entity = await EntitySet.GetAsync<AccessEntity>();
            return QueryResult.Create($1);
        }
    }
}



