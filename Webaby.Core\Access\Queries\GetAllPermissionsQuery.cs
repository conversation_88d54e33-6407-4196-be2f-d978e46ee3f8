﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Webaby.Security;

namespace Webaby.Core.Access.Queries
    {
    public class GetAllPermissionsQuery : QueryBase<BusinessPermissionData>
    {
        public bool IncludeDeleted { get; set; }
    }

    internal class GetAllPermissionsQueryHandler :
        QueryHandlerBase<GetAllPermissionsQuery, BusinessPermissionData>
    {
        public GetAllPermissionsQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<BusinessPermissionData>> ExecuteAsync(GetAllPermissionsQuery query)
    {
            var permissionEntities = await EntitySet.GetAsync<BusinessPermissionEntity>(query.IncludeDeleted);
            return QueryResult.Create(permissionEntities, x => Mapper.Map<BusinessPermissionData>(x));
        }
    }
}

