﻿using System;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DueTime.Queries
    {
    public class GetDueTimeByIdQuery : QueryBase<DueTimeData>
    {
        public Guid Id { get; set; }
    }

    internal class GetDueTimeByIdQueryHandler : QueryHandlerBase<GetDueTimeByIdQuery, DueTimeData>
    {
        public GetDueTimeByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DueTimeData>> ExecuteAsync(GetDueTimeByIdQuery query)
    {
            var mainQuery = await EntitySet.GetAsync<DueTimeEntity>(query.Id);
            return new QueryResult<DueTimeData>(Mapper.Map<DueTimeData>(mainQuery));
        }
    }
}

