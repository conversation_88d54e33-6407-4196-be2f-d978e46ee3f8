﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DueTime.Queries
    {
    public class SearchDueTimeQuery : QueryBase<DueTimeData>
    {
        public static readonly Guid SystemId = Guid.Parse(Guid.Empty.ToString().Replace("0", "F"));
        public Guid? Id { get; set; }
        public string Name { get; set; }
        public bool? DefaultInheritedFromGlobalSettings { get; set; }
    }

    internal class SearchDueTimeQueryHandler : QueryHandlerBase<SearchDueTimeQuery, DueTimeData>
    {
        public SearchDueTimeQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DueTimeData>> ExecuteAsync(SearchDueTimeQuery query)
    {
            var sysDueTime = await EntitySet.GetAsync<DueTimeEntity>(SearchDueTimeQuery.SystemId);
            var main = await EntitySet.GetAsync<DueTimeEntity>();
            if (query.Id.HasValue)
    {
                main = main.Where(x => x.Id == query.Id);
            }
            if (query.DefaultInheritedFromGlobalSettings.HasValue)
    {
                main = main.Where(x => x.DefaultInheritedFromGlobalSettings == query.DefaultInheritedFromGlobalSettings);
            }
            if (!string.IsNullOrWhiteSpace(query.Name))
    {
                main = main.Where(x => x.Name.Contains(query.Name));
            }
            return QueryResult.Create(main, query.Pagination, x => new DueTimeData
    {
                Id = x.Id,
                DefaultInheritedFromGlobalSettings = x.DefaultInheritedFromGlobalSettings,
                Name = x.Name,
                IsWorkingTime = x.IsWorkingTime ?? (sysDueTime.IsWorkingTime ?? false),
                Duration = x.Duration ?? (sysDueTime.Duration ?? 0),
                WorkingSaturday = x.WorkingSaturday ?? (sysDueTime.WorkingSaturday ?? false),
                WorkingSunday = x.WorkingSunday ?? (sysDueTime.WorkingSunday ?? false),
                StartWorkingTime = x.StartWorkingTime ?? (sysDueTime.StartWorkingTime ?? TimeSpan.MinValue),
                EndWorkingTime = x.EndWorkingTime ?? (sysDueTime.EndWorkingTime ?? TimeSpan.MinValue),
                StartBreakTime = x.StartBreakTime ?? (sysDueTime.StartBreakTime ?? TimeSpan.MinValue),
                EndBreakTime = x.EndBreakTime ?? (sysDueTime.EndBreakTime ?? TimeSpan.MinValue),
                VacationDays = x.VacationDays ?? (sysDueTime.VacationDays ?? ""),
                OvertimeDays = x.OvertimeDays ?? (sysDueTime.OvertimeDays ?? ""),
                PercentPassedWhenAlertDue = x.PercentPassedWhenAlertDue ?? (sysDueTime.PercentPassedWhenAlertDue ?? 100)
            });
        }
    }
}

