﻿using System;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetDynamicFieldByIdQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid Id { get; set; }
    }

    internal class GetDynamicFieldByIdQueryHandler : QueryHandlerBase<GetDynamicFieldByIdQuery, DynamicFieldDefinitionData>
    {
        public GetDynamicFieldByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetDynamicFieldByIdQuery query)
    {
            var mainQuery = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>(query.Id);
            return new QueryResult<DynamicFieldDefinitionData>(Mapper.Map<DynamicFieldDefinitionData>(mainQuery));
        }
    }
}

