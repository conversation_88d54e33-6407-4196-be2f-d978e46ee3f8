﻿using System;
using System.Linq;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldInFormByNameQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? FieldId { get; set; }

        public string Name { get; set; }

        public Guid FormId { get; set; }
    }

    internal class GetDynamicFieldInFormByNameQueryHandler :
        QueryHandlerBase<GetDynamicFieldInFormByNameQuery, DynamicFieldDefinitionData>
    {
        public override QueryResult<DynamicFieldDefinitionData> Execute(GetDynamicFieldInFormByNameQuery query)
        {
            var fieldEntities = EntitySet.Get<DynamicFieldDefinitionEntity>();
            if (query.FieldId.HasValue)
            {
                fieldEntities = fieldEntities.Where(x => x.Id != query.FieldId);
            }
            var mainQuery = from field in fieldEntities
                            where field.DynamicFormId == query.FormId && field.Name == query.Name
                            select field;
            return QueryResult.Create(mainQuery, DynamicFieldDefinitionData.FromEntity);
        }
    }
}
