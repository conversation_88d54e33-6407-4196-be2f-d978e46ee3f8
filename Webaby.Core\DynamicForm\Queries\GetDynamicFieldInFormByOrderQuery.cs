﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldInFormByOrderQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? FieldId { get; set; }
        public int Order { get; set; }
        public Guid FormId { get; set; }
    }

    internal class GetDynamicFieldInFormByOrderQueryHandler : QueryHandlerBase<GetDynamicFieldInFormByOrderQuery, DynamicFieldDefinitionData>
    {
        public GetDynamicFieldInFormByOrderQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        {
        }

        // Tối ưu: truy vấn rõ ràng, dùng async/await đúng chuẩn
        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetDynamicFieldInFormByOrderQuery query)
        {
            // Khởi tạo truy vấn các trường động
            var fieldsQuery = EntitySet.Get<DynamicFieldDefinitionEntity>();

            // Loại bỏ trường có FieldId nếu được chỉ định
            if (query.FieldId.HasValue)
            {
                fieldsQuery = fieldsQuery.Where(field => field.Id != query.FieldId.Value);
            }

            // Lọc theo FormId và Order
            fieldsQuery = fieldsQuery.Where(field => field.DynamicFormId == query.FormId && field.Order == query.Order);

            // Thực thi truy vấn bất đồng bộ
            var fields = await fieldsQuery.ToListAsync();

            // Trả về kết quả đã ánh xạ sang DTO
            var result = QueryResult.Create(fields.Select(field => Mapper.Map<DynamicFieldDefinitionData>(field)));
            return result;
        }
    }
}

