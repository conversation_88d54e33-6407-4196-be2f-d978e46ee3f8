﻿using System;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetDynamicFieldSectionByIdQuery : QueryBase<DynamicFieldSectionData>
    {
        public Guid Id { get; set; }
    }

    internal class GetDynamicFieldSectionByIdQueryHandler : QueryHandlerBase<GetDynamicFieldSectionByIdQuery, DynamicFieldSectionData>
    {
        public GetDynamicFieldSectionByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFieldSectionData>> ExecuteAsync(GetDynamicFieldSectionByIdQuery query)
    {
            var mainQuery = await EntitySet.GetAsync<DynamicFieldSectionEntity>(query.Id);
            return new QueryResult<DynamicFieldSectionData>(Mapper.Map<DynamicFieldSectionData>(mainQuery));
        }
    }
}
