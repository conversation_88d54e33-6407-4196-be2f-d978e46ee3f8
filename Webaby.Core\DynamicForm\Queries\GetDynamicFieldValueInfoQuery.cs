﻿using AutoMapper;
using System.Data;
using System.Data.Common;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldValueInfoQuery : QueryBase<DynamicFieldValueInfo>
    {
        public Guid DynamicFormValueId { get; set; }

        public bool? Display { get; set; }

        public bool IncludeDeleted { get; set; }
    }

    internal class GetDynamicFieldValueInfoQueryHandler : QueryHandlerBase<GetDynamicFieldValueInfoQuery, DynamicFieldValueInfo>
    {
        public GetDynamicFieldValueInfoQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<DynamicFieldValueInfo>> ExecuteAsync(GetDynamicFieldValueInfoQuery query)
        {
            DbCommand cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormValueId", query.DynamicFormValueId),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@Display", query.Display),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@IncludeDeleted", query.IncludeDeleted),
            });
            cmd.CommandText = "GetDynamicFieldValueInfo";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<DynamicFieldValueInfo>(cmd);
            return new QueryResult<DynamicFieldValueInfo>(mainQuery);
        }
    }
}