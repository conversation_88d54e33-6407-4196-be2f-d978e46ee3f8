﻿using System;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetDynamicFormValueByIdQuery : QueryBase<DynamicFormValueData>
    {
        public Guid Id { get; set; }
    }

    internal class GetDynamicFormValueByIdQueryHandler : QueryHandlerBase<GetDynamicFormValueByIdQuery, DynamicFormValueData>
    {
        public GetDynamicFormValueByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFormValueData>> ExecuteAsync(GetDynamicFormValueByIdQuery query)
    {
            var entity = await EntitySet.GetAsync<DynamicFormValueEntity>(query.Id);
            return new QueryResult<DynamicFormValueData>(Mapper.Map<DynamicFormValueData>(entity));
        }
    }
}
