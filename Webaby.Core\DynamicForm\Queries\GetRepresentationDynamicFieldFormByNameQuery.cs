﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetRepresentationDynamicFieldFormByNameQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public string RepresentationDynamicFieldName { get; set; }

        public Guid DynamicFormId { get; set; }
    }

    internal class GetRepresentationDynamicFieldFormByNameQueryHandler : QueryHandlerBase<GetRepresentationDynamicFieldFormByNameQuery, DynamicFieldDefinitionData>
    {
        public GetRepresentationDynamicFieldFormByNameQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetRepresentationDynamicFieldFormByNameQuery query)
    {
            var fieldEntities = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>();
            var mainQuery = from field in fieldEntities
                            where field.DynamicFormId == query.DynamicFormId && field.Name.StartsWith(query.RepresentationDynamicFieldName)
                            orderby field.Order
                            select field;
            return QueryResult.Create(mainQuery, x => Mapper.Map<DynamicFieldDefinitionData>(x));
        }
    }
}
