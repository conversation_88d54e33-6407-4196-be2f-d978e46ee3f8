﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Linq.Mapping;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.RegularExpressions;
using Webaby.Data;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetSourceValueOfDynamicFieldQuery : QueryBase<DynamicFieldValueInfo>
    {
        public IEnumerable<DynamicFieldValueInfo> DynamicFieldValueInfo { get; set; }

        public List<Guid> Keys { get; set; }
    }

    internal class GetSourceValueOfDynamicFieldQueryHandler : QueryHandlerBase<GetSourceValueOfDynamicFieldQuery, DynamicFieldValueInfo>
    {
        LambdaExpression GetLambdaCondition<TEntity>(string field, object value) where TEntity : class, IEntity
        {
            ParameterExpression param = Expression.Parameter(typeof(TEntity));
            MemberExpression memberKey = Expression.PropertyOrField(param, field);
            BinaryExpression compare = Expression.Equal(memberKey, Expression.Constant(value));
            return Expression.Lambda(compare, param);
        }

        public object GetValue<T>(string field, List<Guid> key) where T : class, IEntity
        {
            var set = EntitySet.Get<T>();
            T entity = null;
            if (typeof(T).Name.Contains("BusinessSettingEntity"))
            {
                entity = set.Where((Expression<Func<T, bool>>)GetLambdaCondition<T>("Name", field)).FirstOrDefault();
            }
            else
            {
                entity = set.Where(x => key.Contains(x.Id)).FirstOrDefault();
            }
            if (entity != null)
            {
                if (typeof(T).Name.Contains("BusinessSettingEntity"))
                {
                    field = "Value";
                }
                try
                {
                    var val = typeof(T).GetProperty(field).GetValue(entity);
                    if (val != null)
                    {
                        return val.GetText();
                    }
                    return val;
                }
                catch
                {
                    return null;
                }
            }
            return null;
        }

        public DynamicFieldValueInfo GetValue(DynamicFieldValueInfo linkField, List<Guid> key)
        {
            if (linkField.FieldType != FieldType.Linked)
            {
                return linkField;
            }
            var pattern = @"^(((\[(?'first'.+)\])|(?'first'[^\[\]\s]+))\.)?((\[(?'last'.+)\])|(?'last'[^\[\]\s]+))$";
            var validate = new Regex(pattern);
            var entityType = AppDomain.CurrentDomain.GetAssemblies().Where(a => a.FullName.Contains("Webaby", StringComparison.OrdinalIgnoreCase) || a.FullName.Contains("TinyCRM", StringComparison.OrdinalIgnoreCase)).SelectMany(x => x.GetTypes()).First(x => x.GetCustomAttribute(typeof(TableAttribute)) != null && validate.Match(linkField.SourceTableName).Groups["last"].Value == validate.Match((x.GetCustomAttribute(typeof(TableAttribute)) as TableAttribute).Name).Groups["last"].Value);
            object val = this.GetType().GetMethod("GetValue", new Type[] { typeof(string), typeof(List<Guid>) }).MakeGenericMethod(entityType).Invoke(this, new object[] { linkField.SourceFieldName, key });
            linkField.Value = val == null ? "" : val.ToString();
            return linkField;
        }

        public override QueryResult<DynamicFieldValueInfo> Execute(GetSourceValueOfDynamicFieldQuery query)
        {
            query.DynamicFieldValueInfo = query.DynamicFieldValueInfo.Select(x => GetValue(x, query.Keys));
            return QueryResult.Create(query.DynamicFieldValueInfo);
        }
    }
}
