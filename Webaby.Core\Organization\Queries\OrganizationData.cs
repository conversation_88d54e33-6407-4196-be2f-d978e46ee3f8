﻿using System.Collections.Specialized;
using Webaby.EntityData;
using Webaby.Web;
using Webaby;

namespace TinyCRM.Organization.Queries
{
    public class OrganizationData : IEntityData
    {
        public int? NodeLevel { get; set; }
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Code { get; set; }

        public int Type { get; set; }

        public string OrganizationType { get; set; }

        public bool HasOneStopDepartment { get; set; }

        public bool CanBeSuggestedAppointment { get; set; }

        public Guid? ParentId { get; set; }

        public string ParentName { get; set; }

        public DateTime CreatedDate { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public Boolean Deleted { get; set; }

        public DateTime? DeletedDate { get; set; }

        public Guid? DeletedBy { get; set; }

        public int TaskSum { get; set; }

        private readonly IQueryExecutor _queryExecutor;
        public OrganizationData(IQueryExecutor queryExecutor)
        {
            _queryExecutor = queryExecutor;
        }

        public bool TryMapping(string objectName,
            NameValueCollection values,
            HttpPostedFileCollection files,
            out Dictionary<string, List<string>> validateErrors)
        {
            validateErrors = new Dictionary<string, List<string>>();
            var idValue = values[objectName + ".Id"];
            if (!string.IsNullOrEmpty(idValue))
            {
                Id = new Guid(values[objectName + ".Id"]);
            }
            return !validateErrors.Any();
        }

        public async Task SaveAsync()
        {
        }

        public async Task LoadAsync()
        {
            if (Id != Guid.Empty)
            {
                var organizationData = await _queryExecutor.ExecuteOneAsync(new GetOrganizationByIdQuery(this.Id));
                Name = organizationData.Name;
                Code = organizationData.Code;
            }
        }

        public bool HasValue(string objectName, NameValueCollection values, HttpPostedFileCollection files)
        {
            var idValue = values[objectName + ".Id"];
            return (idValue.IsNotNullOrEmpty() && idValue.Trim().IsNotNullOrEmpty());
        }

        public Dictionary<string, string> GetAdditionalMetadata(Guid? dynamicFieldDefinitionId = null, Guid? dynamicFieldValueId = null)
        {
            var rsDict = new Dictionary<string, string>();
            rsDict.Add("LoadUrl", "/Organization/GetOrganizationJsTree");
            rsDict.Add("TokenText", "Text");
            rsDict.Add("Order", "Order");
            return rsDict;
        }

        public string DisplayContent(string templateHint, bool isHtml = false)
        {
            return Name;
        }

        public string GetValue()
        {
            return Id.ToString();
        }

        public bool ExecuteExpression(string expression)
        {
            return true;
        }
    }
}