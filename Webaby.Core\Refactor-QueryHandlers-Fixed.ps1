<#
.SYNOPSIS
  Refactor QueryHandler → async + ctor + mapping → update .csproj (FIXED VERSION)
.DESCRIPTION
  - Với mỗi class *QueryHandler* trong thư mục Queries của modules:
      1) Thêm thiếu usings
      2) Chèn constructor (nếu chưa có)
      3) Chỉ thực hiện với những EntitySet.Get<T>(...) không có Where và không có ExecuteReadCommand:
         a) Đổi signature `Execute` → `async Task<QueryResult<T>> ExecuteAsync(...)`
         b) Thay `EntitySet.Get<T>(...)` → `await EntitySet.GetAsync<T>(...)`
         c) Mapping động `new QueryResult<T>(X.FromEntity(...))` & `QueryResult.Create(...)`
  - Cuối cùng cập nhật `.csproj` với `<Compile Include="…"/>`.
#>
[CmdletBinding(SupportsShouldProcess=$true)]
param(
  [string]   $RootPath   = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core',
  [string[]] $Modules    = @('Access','BusinessSettings'),
  [string]   $CsProjFile = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core\Webaby.Core.csproj'
)

$modified = [System.Collections.Generic.List[string]]::new()

foreach ($mod in $Modules) {
  $dir = Join-Path (Join-Path $RootPath $mod) 'Queries'
  if (-not (Test-Path $dir)) { continue }

  Get-ChildItem $dir -Recurse -Filter '*.cs' | ForEach-Object {
    $file = $_.FullName
    if (-not (Select-String -Path $file -Pattern 'class\s+\w+QueryHandler' -Quiet)) { return }
    if (-not $PSCmdlet.ShouldProcess($file,'Refactor')) { return }

    Write-Host "Refactoring: $file"
    $text = Get-Content -Raw -Path $file

    # Skip files with .Where() patterns
    if ($text -match 'EntitySet\.Get<[^>]+>\([^)]*\)\.Where\(') {
      Write-Host "   Skipped: Contains EntitySet.Get<T>().Where() pattern"
      return
    }

    # Skip files with ExecuteReadCommand
    if ($text -match 'ExecuteReadCommand') {
      Write-Host "   Skipped: Contains ExecuteReadCommand pattern"
      return
    }

    # 1) Add missing usings
    $needed = @(
      'using AutoMapper;',
      'using Webaby.Data;',
      'using Webaby.Localization;',
      'using Webaby;'
    )
    $lines = $text -split "`r?`n"
    $usings = $lines | Select-String '^using .+;$'

    if ($usings) {
      # Có using statements - thêm vào cuối danh sách using
      $idx = ($usings | ForEach-Object LineNumber | Measure-Object -Maximum).Maximum - 1
      foreach ($u in $needed) {
        if ($text -notmatch [regex]::Escape($u)) {
          $lines = $lines[0..$idx] + $u + $lines[($idx+1)..($lines.Length-1)]
          $idx++
        }
      }
    } else {
      # Không có using statements - thêm vào đầu file trước namespace
      $namespaceIdx = 0
      for ($i = 0; $i -lt $lines.Length; $i++) {
        if ($lines[$i] -match '^\s*namespace\s+') {
          $namespaceIdx = $i
          break
        }
      }
      # Thêm using statements trước namespace
      $beforeNamespace = if ($namespaceIdx -gt 0) { $lines[0..($namespaceIdx-1)] } else { @() }
      $afterNamespace = $lines[$namespaceIdx..($lines.Length-1)]
      $lines = $beforeNamespace + $needed + @('') + $afterNamespace
    }
    $text = $lines -join "`r`n"

    # 2) Remove duplicate constructors first
    $text = [regex]::Replace($text,
      '(\s+public\s+\w+QueryHandler\s*\([^}]+\}\s*)\s+public\s+\w+QueryHandler\s*\([^}]+\}',
      '$1',
      [System.Text.RegularExpressions.RegexOptions]::Singleline
    )

    # 3) Add constructor if not exists
    if ($text -notmatch 'public\s+\w+QueryHandler\s*\(') {
      $text = $text -replace '(?ms)(internal\s+class\s+(\w+QueryHandler)\s*:\s*[^{]+)\s*\{', @'
$1
    {
        public $2(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
'@
    }

    # 4) Fix method signature Execute → ExecuteAsync
    $text = $text -replace 'public override\s+QueryResult<(\w+)>\s+Execute', 'public override async Task<QueryResult<$1>> ExecuteAsync'

    # 5) Replace EntitySet.Get<T>(...) → await EntitySet.GetAsync<T>(...)
    $text = [regex]::Replace($text,
      'EntitySet\.Get<([^>]+)>\(\s*([^\)]*)\)(?!\.Where)',
      'await EntitySet.GetAsync<$1>($2)'
    )

    # 6) Fix mapping new QueryResult<T>(X.FromEntity(var))
    $text = [regex]::Replace(
      $text,
      'new\s+QueryResult<(\w+)>\(\s*\w+\.FromEntity\(\s*(\w+)\s*\)\s*\)',
      'new QueryResult<$1>(Mapper.Map<$1>($2))'
    )

    # 7) Fix mapping QueryResult.Create(entity, X.FromEntity)
    $text = [regex]::Replace(
      $text,
      'QueryResult\.Create\(\s*(\w+)\s*,\s*(\w+)\.FromEntity',
      'QueryResult.Create($1, x => Mapper.Map<$2>(x))'
    )

    # 8) Fix extra parentheses
    $text = $text -replace '\)\)\);', '));'

    # 9) Fix indentation issues
    $text = $text -replace '(?m)^\s*\{\s*$', '    {'
    $text = $text -replace '(?m)^(\s+)public\s+(\w+QueryHandler)', '        public $2'

    # Save file
    Set-Content -Path $file -Value $text -Encoding UTF8
    $modified.Add($file)
  }
}

# Update .csproj
if ($modified.Count -gt 0 -and $PSCmdlet.ShouldProcess($CsProjFile,'Update .csproj')) {
  Write-Host "Updating csproj: $CsProjFile"
  [xml]$proj = Get-Content -Path $CsProjFile
  $ns   = $proj.Project.NamespaceURI
  $ig   = $proj.Project.ItemGroup | Where-Object { $_.Compile } | Select-Object -First 1
  if (-not $ig) {
    $ig = $proj.CreateElement('ItemGroup',$ns)
    $proj.Project.AppendChild($ig) | Out-Null
  }
  $base = Split-Path -Path $CsProjFile -Parent
  $uri  = New-Object System.Uri("$base\")
  foreach ($f in $modified) {
    $rel = $uri.MakeRelativeUri((New-Object System.Uri($f))).ToString() -replace '/', '\'
    if (-not ($ig.Compile | Where-Object { $_.Include -eq $rel })) {
      if ($PSCmdlet.ShouldProcess($rel,'Add Compile Include')) {
        $node = $proj.CreateElement('Compile',$ns)
        $node.SetAttribute('Include',$rel)
        $ig.AppendChild($node) | Out-Null
        Write-Host "   + Added Compile Include='$rel'"
      }
    }
  }
  $proj.Save($CsProjFile)
  Write-Host 'Done.'
}
else {
  Write-Host 'No changes.'
}
