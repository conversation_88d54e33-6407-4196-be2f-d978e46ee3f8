<#
.SYNOPSIS
  Refactor QueryHandler → async + ctor + mapping → update .csproj
.DESCRIPTION
  - Với mỗi class *QueryHandler* trong thư mục Queries của modules:
      1) Thêm thiếu usings
      2) Chèn constructor
      3) Chỉ thực hiện với những EntitySet.Get<T>(...) không có Where:
         a) Đổi signature `Execute` → `async Task<QueryResult<T>> ExecuteAsync(...)`
         b) Thay `EntitySet.Get<T>(...)` → `await EntitySet.GetAsync<T>(...)`
         c) Mapping động `new QueryResult<T>(X.FromEntity(...))` & `QueryResult.Create(...)`
  - Cuối cùng cập nhật `.csproj` với `<Compile Include="…"/>`.
#>
[CmdletBinding(SupportsShouldProcess=$true)]
param(
  [string]   $RootPath   = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core',
  [string[]] $Modules    = @('Access','BusinessSettings'),
  [string]   $CsProjFile = 'C:\Users\<USER>\source\Workspaces\CEP_NETCORE\Webaby.Core\Webaby.Core.csproj'
)

$modified = [System.Collections.Generic.List[string]]::new()

foreach ($mod in $Modules) {
  $dir = Join-Path (Join-Path $RootPath $mod) 'Queries'
  if (-not (Test-Path $dir)) { continue }

  Get-ChildItem $dir -Recurse -Filter '*.cs' | ForEach-Object {
    $file = $_.FullName
    if (-not (Select-String -Path $file -Pattern 'class\s+\w+QueryHandler' -Quiet)) { return }
    if (-not $PSCmdlet.ShouldProcess($file,'Refactor')) { return }

    Write-Host "Refactoring: $file"
    $text = Get-Content -Raw -Path $file

    # 1) Thêm thiếu usings
    $needed = @(
      'using System.Threading.Tasks;',
      'using AutoMapper;',
      'using Webaby.Data;',
      'using Webaby.Localization;',
      'using Webaby;'
    )
    $lines = $text -split "`r?`n"
    $usings = $lines | Select-String '^using .+;$'
    if ($usings) {
      $idx = ($usings | ForEach-Object LineNumber | Measure-Object -Maximum).Maximum - 1
      foreach ($u in $needed) {
        if ($text -notmatch [regex]::Escape($u)) {
          $lines = $lines[0..$idx] + $u + $lines[($idx+1)..($lines.Length-1)]
          $idx++
        }
      }
      $text = $lines -join "`r`n"
    }

    # 2) Chèn constructor
    $text = $text -replace '(?ms)(internal\s+class\s+(\w+QueryHandler)\s*:\s*[^{]+)\{', @'
$1 {
    public $2(
        IEntitySet entitySet,
        IRepository repository,
        IText text,
        IMapper mapper)
      : base(entitySet, repository, text, mapper)
    { }
'@

    # Kiểm tra xem có EntitySet.Get<T>().Where() không - nếu có thì bỏ qua
    if ($text -match 'EntitySet\.Get<[^>]+>\([^)]*\)\.Where\(') {
      Write-Host "   Skipped: Contains EntitySet.Get<T>().Where() pattern"
      return
    }

    # Chỉ xử lý EntitySet.Get<T>(...) không có Where
    # 3a) Đổi signature Execute → async Task<QueryResult<T>> ExecuteAsync
    $text = $text -replace 'public override\s+QueryResult<(\w+)>\s+Execute', 'public override async Task<QueryResult<$1>> ExecuteAsync'

    # 3b) Thay EntitySet.Get<T>(...) → await EntitySet.GetAsync<T>(...) (chỉ khi không có .Where)
    $text = [regex]::Replace($text,
      'EntitySet\.Get<([^>]+)>\(\s*([^\)]*)\)(?!\.Where)',
      'await EntitySet.GetAsync<$1>($2)'
    )

    # 3c) Mapping new QueryResult<T>(X.FromEntity(var))
    $text = [regex]::Replace(
      $text,
      'new\s+QueryResult<(\w+)>\(\s*\w+\.FromEntity\(\s*(\w+)\s*\)\s*\)',
      'new QueryResult<$1>(Mapper.Map<$1>($2))'
    )

    # 3d) Mapping QueryResult.Create(entity, X.FromEntity)
    $text = [regex]::Replace(
      $text,
      'QueryResult\.Create\(\s*(\w+)\s*,\s*(\w+)\.FromEntity',
      'QueryResult.Create($1, x => Mapper.Map<$2>(x))'
    )

    # Lưu file
    Set-Content -Path $file -Value $text -Encoding UTF8
    $modified.Add($file)
  }
}

# 4) Cập nhật .csproj
if ($modified.Count -gt 0 -and $PSCmdlet.ShouldProcess($CsProjFile,'Update .csproj')) {
  Write-Host "Updating csproj: $CsProjFile"
  [xml]$proj = Get-Content -Path $CsProjFile
  $ns   = $proj.Project.NamespaceURI
  $ig   = $proj.Project.ItemGroup | Where-Object { $_.Compile } | Select-Object -First 1
  if (-not $ig) {
    $ig = $proj.CreateElement('ItemGroup',$ns)
    $proj.Project.AppendChild($ig) | Out-Null
  }
  $base = Split-Path -Path $CsProjFile -Parent
  $uri  = New-Object System.Uri("$base\")
  foreach ($f in $modified) {
    $rel = $uri.MakeRelativeUri((New-Object System.Uri($f))).ToString() -replace '/', '\'
    if (-not ($ig.Compile | Where-Object { $_.Include -eq $rel })) {
      if ($PSCmdlet.ShouldProcess($rel,'Add Compile Include')) {
        $node = $proj.CreateElement('Compile',$ns)
        $node.SetAttribute('Include',$rel)
        $ig.AppendChild($node) | Out-Null
        Write-Host "   + Added Compile Include='$rel'"
      }
    }
  }
  $proj.Save($CsProjFile)
  Write-Host 'Done.'
}
else {
  Write-Host 'No changes.'
}
