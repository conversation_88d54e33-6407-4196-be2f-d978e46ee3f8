<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Access\**" />
    <Compile Remove="BusinessSettings\**" />
    <Compile Remove="DatabaseDMV\**" />
    <Compile Remove="DueTime\**" />
    <Compile Remove="DynamicForm\**" />
    <Compile Remove="File\**" />
    <Compile Remove="Organization\**" />
    <Compile Remove="PredefinedList\**" />
    <Compile Remove="Role\**" />
    <Compile Remove="Sequence\**" />
    <Compile Remove="UserAccount\**" />
    <EmbeddedResource Remove="Access\**" />
    <EmbeddedResource Remove="BusinessSettings\**" />
    <EmbeddedResource Remove="DatabaseDMV\**" />
    <EmbeddedResource Remove="DueTime\**" />
    <EmbeddedResource Remove="DynamicForm\**" />
    <EmbeddedResource Remove="File\**" />
    <EmbeddedResource Remove="Organization\**" />
    <EmbeddedResource Remove="PredefinedList\**" />
    <EmbeddedResource Remove="Role\**" />
    <EmbeddedResource Remove="Sequence\**" />
    <EmbeddedResource Remove="UserAccount\**" />
    <None Remove="Access\**" />
    <None Remove="BusinessSettings\**" />
    <None Remove="DatabaseDMV\**" />
    <None Remove="DueTime\**" />
    <None Remove="DynamicForm\**" />
    <None Remove="File\**" />
    <None Remove="Organization\**" />
    <None Remove="PredefinedList\**" />
    <None Remove="Role\**" />
    <None Remove="Sequence\**" />
    <None Remove="UserAccount\**" />
    <None Remove="Webaby.Core.csproj.vspscc" />
    <Compile Include="Access\AccessEntity.cs" />
    <Compile Include="Access\ConstCodePermissions.cs" />
    <Compile Include="Access\MenuItemEntity.cs" />
    <Compile Include="DatabaseDMV\Queries\IndexMissingLogData.cs" />
    <Compile Include="DatabaseDMV\Queries\IndexStatisticData.cs" />
    <Compile Include="DueTime\DueTimeEntity.cs" />
    <Compile Include="DueTime\DueTimeInfo.cs" />
    <Compile Include="DueTime\DueTimeReferenceEntity.cs" />
    <Compile Include="DynamicForm\Commands\SubmitDynamicFormValueCommand.cs" />
    <Compile Include="DynamicForm\DynamicFieldDefinitionEntity.cs" />
    <Compile Include="DynamicForm\DynamicFieldSectionEntity.cs" />
    <Compile Include="DynamicForm\DynamicFieldValueEntity.cs" />
    <Compile Include="DynamicForm\DynamicFormContants.cs" />
    <Compile Include="DynamicForm\DynamicFormEntity.cs" />
    <Compile Include="DynamicForm\DynamicFormExtensions.cs" />
    <Compile Include="DynamicForm\DynamicFormValueEntity.cs" />
    <Compile Include="DynamicForm\DynamicValidateOperatorExtensions.cs" />
    <Compile Include="DynamicForm\FormStyle.cs" />
    <Compile Include="DynamicForm\GlobalCalculatedScriptInfo.cs" />
    <Compile Include="DynamicForm\Queries\CalculateDynamicFieldQuery.cs" />
    <Compile Include="DynamicForm\Queries\DynamicAdditionalMetadataInfo.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFieldDefinitionInfo.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFieldValueInfo.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFormListItem.cs" />
    <Compile Include="DynamicForm\Queries\DynamicValidationInfo.cs" />
    <Compile Include="DynamicForm\StaticField.cs" />
    <Compile Include="File\Commands\CreateEditFileCommand.cs" />
    <Compile Include="File\FileEntity.cs" />
    <Compile Include="File\IFileContainer.cs" />
    <Compile Include="File\Queries\FileData.cs" />
    <Compile Include="Organization\OrganizationEntity.cs" />
    <Compile Include="Organization\OrganizationWorkingAreaEntity.cs" />
    <Compile Include="Organization\Queries\GetOrganizationByIdQuery.cs" />
    <Compile Include="Organization\Queries\GetOrganizationListQuery.cs" />
    <Compile Include="Organization\Queries\OrganizationData.cs" />
    <Compile Include="PredefinedList\PredefinedListCategoryEntity.cs" />
    <Compile Include="PredefinedList\PredefinedListConcreteRecordEntity.cs" />
    <Compile Include="PredefinedList\PredefinedListValuesEntity.cs" />
    <Compile Include="PredefinedList\Queries\PredefinedListValuesItem.cs" />
    <Compile Include="Role\Queries\RoleManageData.cs" />
    <Compile Include="Role\RoleManageEntity.cs" />
    <Compile Include="Access\Queries\AccessData.cs" />
    <Compile Include="Access\Queries\BusinessPermissionData.cs" />
    <Compile Include="Access\Queries\RoleBusinessPermissionData.cs" />
    <Compile Include="DueTime\Queries\DueTimeData.cs" />
    <Compile Include="DueTime\Queries\DueTimeReferenceData.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFieldDefinitionData.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFieldSectionData.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFieldValueData.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFormData.cs" />
    <Compile Include="DynamicForm\Queries\DynamicFormValueData.cs" />
    <Compile Include="PredefinedList\Queries\PredefinedListCategoryData.cs" />
    <Compile Include="PredefinedList\Queries\PredefinedListConcreteRecordData.cs" />
    <Compile Include="PredefinedList\Queries\PredefinedListValuesData.cs" />
    <Compile Include="Role\Queries\RoleData.cs" />
    <Compile Include="UserAccount\Queries\UserListItemData.cs" />
    <Compile Include="UserAccount\Queries\UserProfileData.cs" />
    <Compile Include="Access\Queries\GetAccessByIdQuery.cs" />
    <Compile Include="Access\Queries\GetAllAccessQuery.cs" />
    <Compile Include="Access\Queries\GetAllPermissionsQuery.cs" />
    <Compile Include="Access\Queries\GetApiAccessQuery.cs" />
    <Compile Include="DueTime\Queries\GetDueTimeByIdQuery.cs" />
    <Compile Include="DueTime\Queries\GetDueTimeReferenceByObjectIdQuery.cs" />
    <Compile Include="DueTime\Queries\SearchDueTimeQuery.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Webaby\Webaby.csproj" />
  </ItemGroup>
</Project>