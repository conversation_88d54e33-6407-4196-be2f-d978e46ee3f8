﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Reflection;

namespace Webaby.Data
{
    public abstract class RawMigration : Migration
    {
        protected abstract Assembly ResourceAssembly { get; }
        protected virtual bool SuppressTransaction => false;

        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var resourcePath = $"{GetType().Namespace}.{GetType().Name}";
            string resourceFile;
            if (migrationBuilder.IsSqlServer())
            {
                resourceFile = $"{resourcePath}.mssql.sql";
            }
            else if (migrationBuilder.IsNpgsql())
            {
                resourceFile = $"{resourcePath}.postgres.sql";
            }
            else
            {
                throw new NotSupportedException("Database provider not supported.");
            }
            using Stream stream = ResourceAssembly.GetManifestResourceStream(resourceFile) ?? throw new FileNotFoundException($"Resource {resourceFile} not found in assembly {ResourceAssembly.FullName}.");
            using StreamReader reader = new(stream);
            string content = reader.ReadToEnd();
            migrationBuilder.Sql(sql: content, suppressTransaction: SuppressTransaction);
        }
    }
}