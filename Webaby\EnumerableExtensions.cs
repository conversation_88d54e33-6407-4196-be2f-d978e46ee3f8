﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Webaby.Localization;

namespace Webaby
{
    public static class EnumerableExtensions
    {
        public static IEnumerable<TElement> ToArray<TElement>(this String values, String separator = ",")
        {
            values = values == null ? null : values.Trim();
            if (values.IsNullOrEmpty() || values == separator) return Enumerable.Empty<TElement>();
            return Regex.Split(values ?? String.Empty, Regex.Escape(separator)).Select(x => x.To<TElement>());
        }

        public static IEnumerable<TElement> Concat<TElement>(this IEnumerable<TElement> enumerable, params TElement[] values)
        {
            return enumerable.Concat((IEnumerable<TElement>)values);
        }

        public static TResult Pagination<TElement, TResult>(this IQueryable<TElement> enumerable, Int32 index, Int32 size, Func<Int32, IQueryable<TElement>, TResult> selector)
        {
            var total = enumerable.Count();
            if (index < 0) index = 0;
            return selector(total, size == 0
                                       ? Enumerable.Empty<TElement>().AsQueryable()
                                       : size < 0
                                             ? enumerable.Skip(index)
                                             : enumerable.Skip(index).Take(size));
        }

        public static IEnumerable<IEnumerable<TElement>> Partition<TElement>(this IEnumerable<TElement> enumerable, Int32 size = 10)
        {
            var list = new List<TElement>();
            foreach (var element in enumerable)
            {
                list.Add(element);
                if (list.Count >= size)
                {
                    yield return list;
                    list.Clear();
                }
            }
            if (list.Count > 0)
            {
                yield return list;
            }
        }

        public static IEnumerable<TElement> Cycle<TElement>(this IEnumerable<TElement> enumerable)
        {
            var list = new List<TElement>();
            foreach (var item in enumerable)
            {
                list.Add(item);
                yield return item;
            }

            while (true)
            {
                foreach (var item in list)
                {
                    yield return item;
                }
            }
        }

        public static IEnumerable<EachContext<TElement>> Each<TElement>(this IEnumerable<TElement> enumerable)
        {
            var index = 0;
            foreach (var element in enumerable)
            {
                yield return new EachContext<TElement>(index, element);
                index++;
            }
        }


        public static IEnumerable<EachContext<TElement>> Each<TElement>(this IEnumerable<TElement> enumerable, Action<object> value)
        {
            var index = 0;
            foreach (var element in enumerable)
            {
                yield return new EachContext<TElement>(index, element);
                index++;
            }
        }

        public static IDictionary<String, Object> Clone(this Object dic, params String[] excludeKeys)
        {
            return Clone<IDictionary<String, Object>, Object>(dic.GetProperties(), true, excludeKeys);
        }

        public static IDictionary<TKey, TValue> Clone<TDic, TKey, TValue>(this TDic dic, params String[] excludeKeys) where TDic : IDictionary<TKey, TValue>
        {
            return Clone<TDic, TKey, TValue>(dic, EqualityComparer<TKey>.Default, excludeKeys);
        }

        public static IDictionary<String, TValue> Clone<TDic, TValue>(this TDic dic, Boolean ignoreCase, params String[] excludeKeys) where TDic : IDictionary<String, TValue>
        {
            return Clone<TDic, String, TValue>(dic, ignoreCase ? StringComparer.OrdinalIgnoreCase : StringComparer.Ordinal, excludeKeys);
        }

        public static IDictionary<TKey, TValue> Clone<TDic, TKey, TValue>(this TDic dic, IEqualityComparer<TKey> comparer, params String[] excludeKeys) where TDic : IDictionary<TKey, TValue>
        {
            var copyDic = new Dictionary<TKey, TValue>(comparer);
            foreach (var pair in dic)
            {
                copyDic[pair.Key] = pair.Value;
            }
            return copyDic;
        }

        public static string GetDescription<TEnum>(this TEnum value)
        {
            var fi = value.GetType().GetField(value.ToString());

            if (fi != null)
            {
                var attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

                if (attributes.Length > 0)
                {
                    return attributes[0].Description;
                }
            }

            return value.ToString();
        }

        /// <summary>
        /// Build a select list for an enum
        /// </summary>
        public static SelectList SelectListFor<T>() where T : struct
        {
            Type t = typeof(T);
            return !t.IsEnum ? null
                             : new SelectList(BuildSelectListItems<T>(), "Value", "Text");
        }

        /// <summary>
        /// Build a select list for an enum with a particular value selected 
        /// </summary>
        public static SelectList SelectListFor<T>(T selected) where T : struct
        {
            Type t = typeof(T);
            return !t.IsEnum ? null
                             : new SelectList(BuildSelectListItems<T>(), "Value", "Text", selected.ToString());
        }

        public static IEnumerable<SelectListItem> BuildSelectListItems<T>()
        {
            return Enum.GetValues(typeof(T))
                       .Cast<Enum>()
                       .Select(e => new SelectListItem { Value = e.ToString(), Text = e.GetDescription() });
        }

    }
}