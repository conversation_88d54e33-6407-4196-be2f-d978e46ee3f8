﻿using Webaby.EntityData;

namespace Webaby
{
    public interface IUserDefinedTableGridData
    {
        Guid Id { get; set; }

        Guid ReferenceObjectId { get; set; }

        Guid OnDynamicFieldId { get; set; }

        Guid DynamicDefinedTableSchemaId { get; set; }

        void Load();

        IEntityData CopyDynamicFieldValue();

        IEntityData CopyDynamicFieldValueByName(Guid toDynamicDefinedTableSchemaId, Guid? toDynamicFieldValueId, string toDynamicFieldValue);

        List<DynamicDefinedTableCellValueBase> GetBaseListItems();
    }

    public class DynamicDefinedTableCellValueBase
    {
        public Guid? Id { get; set; }

        public Guid DynamicDefinedTableColumnId { get; set; }

        public Guid DynamicFieldValueId { get; set; }

        public int RowNumber { get; set; }

        public string Value { get; set; }
    }
}