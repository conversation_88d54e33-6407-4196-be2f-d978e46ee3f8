﻿using Flee.PublicTypes;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Webaby
{
    public static class StringExtensions
    {
        public static Boolean IsValidExtension(this String path, String allowedExtensions)
        {
            if (path.IsNullOrEmpty()) return false;
            var extension = Path.GetExtension(path);
            if (extension.IsNullOrEmpty() || allowedExtensions.IsNullOrEmpty()) return false;
            extension = extension.TrimStart('.');
            return allowedExtensions.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(x => x.Trim().TrimStart('.')).Contains(extension, StringComparer.OrdinalIgnoreCase);
        }

        public static String RemoveDiacriticChars(this String value)
        {
            string normalizedText = value.Normalize(NormalizationForm.FormD);
            normalizedText = normalizedText.Replace("đ", "d").Replace("Đ", "D");
            StringBuilder stringBuilder = new StringBuilder();

            foreach (char c in normalizedText)
            {
                UnicodeCategory unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        public static String Sha1(this String value)
        {
            var algorithm = SHA1.Create();
            var data = algorithm.ComputeHash(Encoding.UTF8.GetBytes(value));
            var result = "";
            for (var i = 0; i < data.Length; i++)
            {
                result += data[i].ToString("x2");
            }
            return result;
        }

        public static String Md5(this String value)
        {
            var algorithm = MD5.Create();
            var data = algorithm.ComputeHash(Encoding.UTF8.GetBytes(value));
            var result = "";
            for (var i = 0; i < data.Length; i++)
            {
                result += data[i].ToString("x2");
            }
            return result;
        }

        public static Boolean IsEqualIgnoreCase(this String value, String other)
        {
            return String.Compare(value, other, StringComparison.OrdinalIgnoreCase) == 0;
        }

        public static Boolean IsEqualIgnoreCase(this String value, params String[] others)
        {
            return others.Any(value.IsEqualIgnoreCase);
        }

        public static String CamelCase(this String value)
        {
            if (String.IsNullOrEmpty(value)) return String.Empty;
            return value.Substring(0, 1).ToLower() + value.Substring(1);
        }

        public static Boolean IsNotNullOrEmpty(this String value)
        {
            return !String.IsNullOrEmpty(value);
        }

        public static String FormatAsInt(this String value, string format)
        {
            int ival = 0;
            if (int.TryParse(value, out ival))
            {
                return ival.ToString(format);
            }
            return value;
        }

        public static Boolean IsNullOrEmpty(this String value)
        {
            return String.IsNullOrEmpty(value);
        }

        public static String ToEmptyIfNull(this String value)
        {
            return String.IsNullOrEmpty(value) ? string.Empty : value;
        }

        public static String Wrap(this String value, String by)
        {
            return Wrap(value, by, by);
        }

        public static String Wrap(this String value, String before, String after)
        {
            return before + value + after;
        }

        public static String Right(this String value, Int32 length)
        {
            if (value.IsNullOrEmpty() || value.Length < length) return String.Empty;
            return value.Substring(value.Length - length);
        }

        public static String Left(this String value, Int32 length)
        {
            if (value.IsNullOrEmpty() || value.Length < length) return String.Empty;
            return value.Substring(0, length);
        }

        public static String Contract(this String value, Int32 length)
        {
            if (value.IsNullOrEmpty() || value.Length <= length * 2) return value;
            return value.Substring(length, value.Length - length * 2);
        }

        public static string NonUnicode(this string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }
            string[] arr1 =
            {
                "á", "à", "ả", "ã", "ạ", "â", "ấ", "ầ", "ẩ", "ẫ", "ậ", "ă", "ắ", "ằ", "ẳ", "ẵ", "ặ",
                "đ",
                "é", "è", "ẻ", "ẽ", "ẹ", "ê", "ế", "ề", "ể", "ễ", "ệ",
                "í", "ì", "ỉ", "ĩ", "ị",
                "ó", "ò", "ỏ", "õ", "ọ", "ô", "ố", "ồ", "ổ", "ỗ", "ộ", "ơ", "ớ", "ờ", "ở", "ỡ", "ợ",
                "ú", "ù", "ủ", "ũ", "ụ", "ư", "ứ", "ừ", "ử", "ữ", "ự",
                "ý", "ỳ", "ỷ", "ỹ", "ỵ"
            };
            string[] arr2 =
            {
                "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a",
                "d",
                "e", "e", "e", "e", "e", "e", "e", "e", "e", "e", "e",
                "i", "i", "i", "i", "i",
                "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o",
                "u", "u", "u", "u", "u", "u", "u", "u", "u", "u", "u",
                "y", "y", "y", "y", "y"
            };
            for (int i = 0; i < arr1.Length; i++)
            {
                text = text.Replace(arr1[i], arr2[i]);
                text = text.Replace(arr1[i].ToUpper(), arr2[i].ToUpper());
            }
            return text;
        }

        public static string RemoveNonAlphanumericChars(this string text)
        {
            Regex rgx = new Regex("[^a-zA-Z0-9_@./#&+-]");
            return rgx.Replace(text, "");
        }

        public static string GetAlphanumericChars(this string text)
        {
            Regex rgx = new Regex("[^a-zA-Z0-9_]");
            return rgx.Replace(text, "");
        }

        public static string GetFirstChars(this string text)
        {
            var chars = text.Split(' ').Select(y => y[0]).ToArray();
            string result = string.Empty;
            result = string.Join("", chars);

            return result;
        }

        public static string ByteArrayToHexString(this byte[] byteArray)
        {
            StringBuilder hex = new StringBuilder(byteArray.Length * 2);
            foreach (var b in byteArray)
            {
                hex.AppendFormat("{0:x2}", b);
            }
            return hex.ToString();
        }

        public static byte[] StringToByteArray(this string hex)
        {
            int numberChars = hex.Length;
            byte[] bytes = new byte[numberChars / 2];
            for (int i = 0; i < numberChars; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(1, 2), 16);
            }
            return bytes;
        }

        public static string ReturnNotNull(params string[] list)
        {
            foreach (var item in list)
            {
                if (!string.IsNullOrWhiteSpace(item))
                {
                    return item;
                }
            }
            return "";
        }

        public static String GetLastWords(this String value, int count)
        {
            if (count <= 0)
            {
                return value;
            }

            value = value.Trim();
            while (value.Contains("  "))
            {
                value = value.Replace("  ", " ");
            }

            string[] words = value.Split(' ');
            if (words.Count() <= count)
            {
                return value;
            }

            string result = words[words.Count() - 1];
            for (int i = 1; i < count; i++)
            {
                result = words[words.Count() - i - 1] + " " + result;
            }

            return result;
        }

        public static String ToUpperFirstChar(this String value)
        {
            if (String.IsNullOrEmpty(value))
            {
                return value;
            }
            return value.First().ToString().ToUpper() + value.Substring(1);
        }

        public static bool Contains(this string src, string toCheck, StringComparison comparison)
        {
            return src?.IndexOf(toCheck, comparison) >= 0;
        }

        public static string Base64Encode(this string plainText)
        {
            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
            return Convert.ToBase64String(plainTextBytes);
        }

        public static string Base64Decode(this string base64Data)
        {
            var base64Bytes = Convert.FromBase64String(base64Data);
            return Encoding.UTF8.GetString(base64Bytes);
        }

        public static string UrlEncodeBase64(this string base64Data)
        {
            char[] padding = { '=' };
            return base64Data.TrimEnd(padding).Replace('+', '-').Replace('/', '_');
        }

        public static object ConvertToBasicType(this string value, Type type)
        {
            try
            {
                object convertValue = type.GetTypeConverter(string.IsNullOrEmpty(value))
                    .ConvertFromString(null, CultureInfo.CurrentCulture, value);
                return convertValue;
            }
            catch
            {
                return null;
            }
        }

        public static Boolean IsEqualIgnoreUnicodeAndCase(this String value, String other)
        {
            string part1 = value.NonUnicode().RemoveNonAlphanumericChars();
            string part2 = other.NonUnicode().RemoveNonAlphanumericChars();

            return part1.IsEqualIgnoreCase(part2);
        }

        public static String ToNumbericString(this object unformatString)
        {
            if (unformatString != null && !string.IsNullOrWhiteSpace(unformatString.ToString()))
            {
                long theValue;
                Int64.TryParse(unformatString.ToString(), out theValue);
                return theValue.ToString("N0");
            }
            return string.Empty;
        }

        public static bool EvaluateBooleanExpression(this string expression, params object[] inputs)
        {
            if (!string.IsNullOrEmpty(expression) && inputs.Length > 0)
            {
                ExpressionContext context = new ExpressionContext();

                VariableCollection variables = context.Variables;
                int inputNumber = 1;
                foreach (var input in inputs)
                {
                    if (input.GetType().IsGenericType)
                    {
                        variables.Add("Input" + inputNumber.ToString(), input);
                    }
                    else
                    {
                        variables.Add("Input" + inputNumber.ToString(), input.ToString());
                    }
                    inputNumber++;
                }

                IGenericExpression<bool> e = context.CompileGeneric<bool>(expression);
                bool result = e.Evaluate();
                return result;
            }
            return false;
        }

        public static bool EvaluateBooleanExpressionByDataTableCompute(this string expression, IEnumerable<EvaluateComputeItem> evaluateComputeItems)
        {
            if (!string.IsNullOrEmpty(expression) && evaluateComputeItems.Count() > 0)
            {
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Temp___Id", typeof(int));
                foreach (var item in evaluateComputeItems)
                {
                    dataTable.Columns.Add(item.Name, item.Type);
                }

                DataRow dataRow = dataTable.NewRow();
                dataRow["Temp___Id"] = 1;
                foreach (var item in evaluateComputeItems)
                {
                    dataRow[item.Name] = item.Value != null ? item.Value : DBNull.Value;
                }
                dataTable.Rows.Add(dataRow);

                var computeResult = dataTable.Compute("COUNT(Temp___Id)", expression);
                if (computeResult != null)
                {
                    int computeResultNumber = int.Parse(computeResult.ToString());
                    if (computeResultNumber > 0)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public static List<int> AllIndexesOf(this string str, string value)
        {
            if (String.IsNullOrEmpty(str) || String.IsNullOrEmpty(value))
            {
                return null;
            }

            List<int> indexes = new List<int>();
            for (int index = 0; ; index += value.Length)
            {
                index = str.IndexOf(value, index);
                if (index == -1)
                {
                    return indexes;
                }

                indexes.Add(index);
            }
        }

        public static string GenerateCheckSum(this string text)
        {
            int crc = 0xFFFF; // initial value
            int polynomial = 0x1021; // 0001 0000 0010 0001  (0, 5, 12)
            byte[] bytes = Encoding.ASCII.GetBytes(text);
            foreach (byte b in bytes)
            {
                for (int i = 0; i < 8; i++)
                {
                    bool bit = ((b >> (7 - i) & 1) == 1);
                    bool c15 = ((crc >> 15 & 1) == 1);
                    crc <<= 1;
                    if (c15 ^ bit) crc ^= polynomial;
                }
            }
            crc &= 0xFFFF;
            return crc.ToString("X4");
        }

        public static DateTime? ConvertVNDateTimeStringToDateTime(this string inputDate)
        {
            DateTime? dateTime;
            string[] formatTime = { "dd-MM-yyyy", "dd/MM/yyyy" };
            try
            {
                dateTime = DateTime.ParseExact(inputDate, formatTime, CultureInfo.InvariantCulture, DateTimeStyles.None);
            }
            catch (FormatException ex)
            {
                return null;
            }
            return dateTime;
        }

        public static IEnumerable<String> SplitValues(this String value, String separator = ",", Boolean trimSpace = true, Boolean removeEmptys = true)
        {
            return SplitValues<String>(value, separator, trimSpace, removeEmptys);
        }

        public static IEnumerable<T> SplitValues<T>(this String value, String separator = ",", Boolean trimSpace = true, Boolean removeEmptys = true)
        {
            if (!value.IsNullOrEmpty() && trimSpace) value = value.Trim();
            if (String.IsNullOrEmpty(value)) return new T[0];
            var parts = Regex.Split(value, Regex.Escape(separator), RegexOptions.IgnoreCase);
            return parts.Select(x => trimSpace ? x.Trim() : x).Where(x => !removeEmptys || x.Length > 0).Select(x => x.To<T>()).ToArray();
        }
    }

    public class NameValueExpressionOwner
    {
        public string Name { get; set; }

        public string Value { get; set; }
    }

    public class EvaluateComputeItem
    {
        public string Name { get; set; }

        public Type Type { get; set; }

        public object Value { get; set; }
    }
}