﻿using System.ComponentModel;
using System.Reflection;
using System.Runtime.CompilerServices;
using Webaby.EntityData;

namespace Webaby
{
    public static class TypeExtensions
    {
        public static Boolean IsAnonymousType(this Type type)
        {
            if (type == null) throw new ArgumentNullException("type");

            // HACK: The only way to detect anonymous types right now.
            return Attribute.IsDefined(type, typeof(CompilerGeneratedAttribute), false)
                   && type.IsGenericType && type.Name.Contains("AnonymousType")
                   && (type.Name.StartsWith("<>") || type.Name.StartsWith("VB$"))
                   && (type.Attributes & TypeAttributes.NotPublic) == TypeAttributes.NotPublic;
        }

        public static Boolean IsEmpty<T>(this T value)
        {
            return Equals(value, null) || Equals(value, default(T));
        }

        public static Boolean IsEmpty<T>(this T? value) where T : struct
        {
            return !value.HasValue || value.Value.IsEmpty();
        }

        public static Boolean False(this Object value)
        {
            if (value == null
                || Equals(value, Guid.Empty)
                || Equals(value, false)
                || Equals(value, String.Empty)
                || Equals(value, 0)
                || Equals(value, 0D)
                || Equals(value, DateTime.MinValue)
                || Equals(value, DBNull.Value)
                || Equals(value, '\0')) return true;
            return false;
        }

        public static Boolean True(this Object value)
        {
            return !False(value);
        }

        public static T Default<T>(this T value, T def)
        {
            if (Equals(default(T), value) || Equals(value, String.Empty)) return def;
            return value;
        }

        public static Object Default(this Type type)
        {
            if (type.IsClass || type.IsInterface) return null;
            return Activator.CreateInstance(type);
        }

        public static T To<T>(this Object value)
        {
            return To(value, default(T));
        }

        public static T To<T>(this Object value, T def)
        {
            try
            {
                if (Equals(def, null)) def = (T)typeof(T).Default();
                if (value == null) return def;
                if (typeof(T) == typeof(Guid)) return (T)(Object)Guid.Parse(value.ToString());
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception)
            {
                return def;
            }
        }

        public static String TryToString(this Object value)
        {
            return value == null ? String.Empty : value.ToString();
        }

        public static TypeConverter GetTypeConverter(this Type type, bool isValueNull)
        {
            Type underlyingType = null;
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                underlyingType = Nullable.GetUnderlyingType(type);
            }
            TypeConverter converter = TypeDescriptor.GetConverter(type);
            if (!isValueNull && underlyingType != null)
            {
                converter = TypeDescriptor.GetConverter(underlyingType);
            }
            return converter;
        }

        public static bool IsNumericType(this Type o)
        {
            var type = o;
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                type = Nullable.GetUnderlyingType(type);
            }
            switch (Type.GetTypeCode(type))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    return true;
                default:
                    return false;
            }
        }

        public static bool IsCollection(this Type type)
        {
            bool isCollection = false;
            if (type.IsGenericType)
            {
                foreach (Type @interface in type.GetInterfaces())
                {
                    if (@interface.IsGenericType && (@interface.GetGenericTypeDefinition() == typeof(ICollection<>) || @interface.GetGenericTypeDefinition() == typeof(IList<>)))
                    {
                        isCollection = true;
                    }
                }
            }
            return isCollection;
        }

        public static Type BaseType(this Type type)
        {
            if (type.IsGenericType)
            {
                return type.GetGenericArguments()[0];
            }
            return type;
        }

        public static bool IsEntityData(this Type type)
        {
            return typeof(IEntityData).IsAssignableFrom(type);
        }

        public static bool IsGridData(this Type type)
        {
            return typeof(IGridData).IsAssignableFrom(type);
        }

        public static bool IsUserDefinedTableGridData(this Type type)
        {
            return typeof(IUserDefinedTableGridData).IsAssignableFrom(type);
        }

        public static Type ToType(this string typeName)
        {
            var type = Type.GetType(typeName);
            if (type == null)
            {
                var assembliesList = AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assemblyName in assembliesList)
                {
                    Assembly assembly = Assembly.Load(assemblyName.FullName);
                    var assemblyType = assembly.GetType(typeName);
                    if (assemblyType != null)
                    {
                        type = assemblyType;
                        break;
                    }
                    else
                    {
                        var index = typeName.IndexOf("`1");
                        if (index != -1)
                        {
                            index += 2;
                            var genericType = Type.GetType(typeName.Substring(0, index));
                            var realType = assembly.GetType(typeName.Substring(index + 1, typeName.Length - index - 2));
                            if (genericType != null && realType != null)
                            {
                                type = genericType.MakeGenericType(realType);
                                break;
                            }
                        }
                    }
                }
            }

            return type;
        }
    }
}